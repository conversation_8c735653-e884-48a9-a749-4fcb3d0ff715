      <template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属课程" prop="courseId">
        <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable>
          <el-option
            v-for="course in courseList"
            :key="course.courseId"
            :label="course.courseName"
            :value="course.courseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="题目内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入题目内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目类型" prop="questionType">
        <el-select v-model="queryParams.questionType" placeholder="请选择题目类型" clearable>
          <el-option label="单选题" value="0"></el-option>
          <el-option label="多选题" value="1"></el-option>
          <el-option label="判断题" value="2"></el-option>
          <el-option label="简答题" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['education:bank:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['education:bank:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['education:bank:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['education:bank:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['education:bank:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bankList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="题目ID" align="center" prop="questionId" />
      <el-table-column label="所属课程" align="center" prop="courseName" />
      <el-table-column label="题目内容" align="center" prop="content" show-overflow-tooltip />
      <el-table-column label="题目类型" align="center" prop="questionType">
        <template slot-scope="scope">
          <span>{{ formatType(scope.row.questionType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="难度(1-5)" align="center" prop="difficulty" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['education:bank:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['education:bank:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改题库管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form :model="{ forms, batchCourseId }" :rules="rules" label-width="80px" ref="form">
        <!-- 统一设置课程 -->
        <el-form-item label="所属课程" prop="batchCourseId">
          <el-select v-model="batchCourseId" placeholder="请选择课程" style="width: 100%;">
            <el-option
              v-for="course in courseList"
              :key="course.courseId"
              :label="course.courseName"
              :value="course.courseId"
            ></el-option>
          </el-select>
        </el-form-item>

        <!-- 题目表单区域 -->
        <el-card v-for="(form, index) in forms" :key="index" class="box-card" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span>题目 {{ index + 1 }}</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="removeQuestionForm(index)" icon="el-icon-delete">删除</el-button>
          </div>

          <el-form-item label="题目类型" :prop="'forms.' + index + '.questionType'" :rules="rules.questionType">
            <el-radio-group v-model="form.questionType" @change="type => handleTypeChange(type, index)">
              <el-radio label="0">单选题</el-radio>
              <el-radio label="1">多选题</el-radio>
              <el-radio label="2">判断题</el-radio>
              <el-radio label="3">简答题</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="题目内容" :prop="'forms.' + index + '.content'" :rules="rules.content">
            <el-input v-model="form.content" type="textarea" placeholder="请输入内容" />
          </el-form-item>

          <div v-if="form.questionType === '0' || form.questionType === '1'">
            <el-form-item label="选项">
              <div v-for="(option, oIndex) in form.options" :key="oIndex" class="option-item">
                <el-input v-model="option.content" placeholder="请输入选项内容" style="width: 80%; margin-right: 10px;"></el-input>
                <el-button type="danger" icon="el-icon-delete" circle @click="removeOption(index, oIndex)"></el-button>
              </div>
              <el-button type="primary" @click="addOption(index)">添加选项</el-button>
            </el-form-item>
            <el-form-item label="答案" :prop="'forms.' + index + '.answer'" :rules="rules.answer">
              <el-radio-group v-if="form.questionType === '0'" v-model="form.answer">
                <el-radio v-for="(option, oIndex) in form.options" :key="oIndex" :label="String.fromCharCode(65 + oIndex)">{{ String.fromCharCode(65 + oIndex) }}</el-radio>
              </el-radio-group>
              <el-checkbox-group v-if="form.questionType === '1'" v-model="form.answer">
                 <el-checkbox v-for="(option, oIndex) in form.options" :key="oIndex" :label="String.fromCharCode(65 + oIndex)">{{ String.fromCharCode(65 + oIndex) }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <div v-if="form.questionType === '2'">
            <el-form-item label="答案" :prop="'forms.' + index + '.answer'" :rules="rules.answer">
              <el-radio-group v-model="form.answer">
                <el-radio label="T">正确</el-radio>
                <el-radio label="F">错误</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <div v-if="form.questionType === '3'">
            <el-form-item label="答案" :prop="'forms.' + index + '.answer'" :rules="rules.answer">
              <el-input v-model="form.answer" type="textarea" placeholder="请输入答案" />
            </el-form-item>
          </div>

          <el-form-item label="解析" prop="explanation">
            <el-input v-model="form.explanation" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="难度" prop="difficulty">
            <el-rate v-model="form.difficulty" :max="5"></el-rate>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-card>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="addQuestionForm">继续添加题目</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 题库导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      class="import-dialog"
    >
      <div class="import-container">
        <!-- 步骤指示器 -->
        <el-steps :active="upload.currentStep" finish-status="success" align-center class="import-steps">
          <el-step title="选择配置" icon="el-icon-setting"></el-step>
          <el-step title="上传文件" icon="el-icon-upload"></el-step>
          <el-step title="导入完成" icon="el-icon-success"></el-step>
        </el-steps>

        <!-- 配置区域 -->
        <div class="config-section" v-show="upload.currentStep === 0">
          <el-card shadow="never" class="config-card">
            <div slot="header" class="card-header">
              <i class="el-icon-setting"></i>
              <span>导入配置</span>
            </div>

            <el-form :model="upload" label-width="100px" class="config-form">
              <el-form-item label="所属课程" required>
                <el-select
                  v-model="upload.courseId"
                  placeholder="请选择要导入题目的课程"
                  style="width: 100%;"
                  size="medium"
                >
                  <el-option
                    v-for="course in courseList"
                    :key="course.courseId"
                    :label="course.courseName"
                    :value="course.courseId"
                  >
                    <span style="float: left">{{ course.courseName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ course.courseId }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="重复处理">
                <el-radio-group v-model="upload.updateSupport" size="medium">
                  <el-radio :label="0" class="radio-option">
                    <div class="radio-content">
                      <span class="radio-label">
                        <i class="el-icon-circle-close" style="color: #E6A23C;"></i>
                        跳过已存在
                      </span>
                      <span class="radio-desc">
                        当发现题目内容和类型相同时，跳过导入，保留原有题目不变
                      </span>
                      <span class="radio-note">
                        <i class="el-icon-info"></i>
                        判定依据：题目内容 + 题目类型 + 选项内容（选择题）
                      </span>
                    </div>
                  </el-radio>
                  <el-radio :label="1" class="radio-option">
                    <div class="radio-content">
                      <span class="radio-label">
                        <i class="el-icon-refresh" style="color: #409EFF;"></i>
                        覆盖已存在
                      </span>
                      <span class="radio-desc">
                        当发现重复题目时，用新数据完全覆盖原有题目的所有信息
                      </span>
                      <span class="radio-note">
                        <i class="el-icon-warning"></i>
                        注意：原题目的解析、难度等信息将被新数据替换
                      </span>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>

            <div class="template-section">
              <el-alert
                title="导入说明"
                type="info"
                :closable="false"
                show-icon
              >
                <div slot="default">
                  <p><strong>📋 数据格式要求：</strong></p>
                  <p>• 请先下载标准模板，严格按照模板格式填写题目数据</p>
                  <p>• 支持单选题(0)、多选题(1)、判断题(2)、简答题(3)四种题型</p>
                  <p>• 文件格式仅支持 .xlsx 和 .xls，大小不超过5MB</p>

                  <p><strong>🔍 重复判定规则：</strong></p>
                  <p>• 系统会自动检测题目内容、题目类型和选项内容</p>
                  <p>• 选择题会同时比较选项内容，确保准确判定</p>
                  <p>• 内容会自动标准化处理（去除多余空格、统一大小写）</p>

                  <p><strong>⚠️ 注意事项：</strong></p>
                  <p>• 题目内容不能为空，长度不超过500字符</p>
                  <p>• 选择题至少需要2个选项，答案格式要正确</p>
                  <p>• 判断题答案只能是：是/否、对/错、正确/错误</p>
                </div>
              </el-alert>

              <div class="template-download">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  @click="importTemplate"
                  size="medium"
                  class="download-btn"
                >
                  下载导入模板
                </el-button>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-section" v-show="upload.currentStep === 1">
          <el-card shadow="never" class="upload-card">
            <div slot="header" class="card-header">
              <i class="el-icon-upload"></i>
              <span>文件上传</span>
            </div>

            <el-upload
              ref="upload"
              :limit="1"
              accept=".xlsx,.xls"
              :headers="upload.headers"
              :action="upload.url"
              :data="{ courseId: upload.courseId, updateSupport: upload.updateSupport }"
              :disabled="upload.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :auto-upload="false"
              :on-change="handleFileChange"
              drag
              class="upload-dragger"
            >
              <div class="upload-content">
                <i class="el-icon-upload upload-icon"></i>
                <div class="upload-text">
                  <p class="upload-title">将Excel文件拖到此处，或点击上传</p>
                  <p class="upload-hint">支持 .xlsx、.xls 格式，文件大小不超过5MB</p>
                </div>
              </div>
            </el-upload>

            <div class="file-info" v-if="upload.fileName">
              <el-tag type="success" size="medium">
                <i class="el-icon-document"></i>
                {{ upload.fileName }}
              </el-tag>
            </div>
          </el-card>
        </div>

        <!-- 导入结果区域 -->
        <div class="result-section" v-show="upload.currentStep === 2">
          <el-card shadow="never" class="result-card">
            <div slot="header" class="card-header">
              <i class="el-icon-success"></i>
              <span>导入结果</span>
            </div>

            <div class="result-content" v-if="upload.result">
              <div class="result-summary">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-number total">{{ upload.result.totalRows || 0 }}</div>
                      <div class="stat-label">总计行数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-number success">{{ upload.result.successRows || 0 }}</div>
                      <div class="stat-label">成功导入</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-number error">{{ upload.result.failRows || 0 }}</div>
                      <div class="stat-label">导入失败</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-number skip">{{ upload.result.skipRows || 0 }}</div>
                      <div class="stat-label">跳过行数</div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 错误详情 -->
              <div class="error-details" v-if="upload.result.errors && upload.result.errors.length > 0">
                <el-collapse>
                  <el-collapse-item title="错误详情" name="errors">
                    <div class="error-list">
                      <div
                        v-for="(error, index) in upload.result.errors"
                        :key="index"
                        class="error-item"
                      >
                        <el-tag type="danger" size="mini">第{{ error.rowNumber }}行</el-tag>
                        <span class="error-message">{{ error.message }}</span>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>

              <!-- 警告信息 -->
              <div class="warning-details" v-if="upload.result.warnings && upload.result.warnings.length > 0">
                <el-collapse>
                  <el-collapse-item title="警告信息" name="warnings">
                    <div class="warning-list">
                      <div
                        v-for="(warning, index) in upload.result.warnings"
                        :key="index"
                        class="warning-item"
                      >
                        <el-tag type="warning" size="mini">第{{ warning.rowNumber }}行</el-tag>
                        <span class="warning-message">{{ warning.message }}</span>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <template v-if="upload.currentStep === 0">
          <el-button @click="upload.open = false">取 消</el-button>
          <el-button type="primary" @click="nextStep" :disabled="!upload.courseId">下一步</el-button>
        </template>

        <template v-else-if="upload.currentStep === 1">
          <el-button @click="prevStep">上一步</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="upload.isUploading"
            @click="submitFileForm"
            :disabled="!upload.fileName"
          >
            开始导入
          </el-button>
        </template>

        <template v-else-if="upload.currentStep === 2">
          <el-button @click="resetImport">重新导入</el-button>
          <el-button type="primary" @click="finishImport">完 成</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBank, getBank, delBank, addBank, updateBank, downloadTemplate } from "@/api/education/bank";
import { listCourse } from "@/api/education/course";

export default {
  name: "Bank",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 是否显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 题库管理表格数据
      bankList: [],
      // 课程列表
      courseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseId: null,
        content: null,
        questionType: null,
      },
      // 批量添加的表单参数
      forms: [],
      // 批量添加时共享的课程ID
      batchCourseId: null,
      // 表单校验
      rules: {
        courseId: [{ required: true, message: "所属课程不能为空", trigger: "change" }],
        questionType: [{ required: true, message: "题目类型不能为空", trigger: "change" }],
        content: [{ required: true, message: "题目内容不能为空", trigger: "blur" }],
        answer: [{ required: true, message: "答案不能为空", trigger: "blur" }],
      },
      // 上传参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的题目数据
        updateSupport: 0,
        // 课程ID
        courseId: null,
        // 当前步骤
        currentStep: 0,
        // 上传的文件名
        fileName: '',
        // 导入结果
        result: null,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/education/bank/importData"
      }
    };
  },
  created() {
    this.getList();
    this.getCourseList();
  },
  methods: {
    /** 查询题库管理列表 */
    getList() {
      this.loading = true;
      listBank(this.queryParams).then(response => {
        this.bankList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询课程列表 */
    getCourseList() {
      listCourse({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.courseList = response.rows;
      });
    },
    // 格式化题目类型
    formatType(type) {
      const typeMap = { '0': '单选题', '1': '多选题', '2': '判断题', '3': '简答题' };
      return typeMap[type];
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 创建一个标准的新题目对象
    createNewQuestion() {
      return {
        questionId: null,
        courseId: null, // 将由 batchCourseId 统一设置
        questionType: '0',
        content: '',
        options: [{ content: '' }, { content: '' }],
        answer: '',
        explanation: '',
        difficulty: 3,
        remark: '',
      };
    },
    // 表单重置
    reset() {
      this.forms = [this.createNewQuestion()];
      this.batchCourseId = null;
      // 使用$nextTick确保DOM更新后再重置表单
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.resetForm("form");
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.questionId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.title = "批量添加题目";
      // 在对话框打开后再重置表单
      this.$nextTick(() => {
        this.reset();
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const questionId = row.questionId || this.ids[0];
      this.open = true;
      this.title = "修改题目";

      this.$nextTick(() => {
        getBank(questionId).then(response => {
          const newQuestion = this.createNewQuestion();
          const questionData = Object.assign(newQuestion, response.data);

          if (!questionData.options || !Array.isArray(questionData.options) || questionData.options.length === 0) {
            questionData.options = [{ content: '' }];
          }

          if (questionData.questionType === '1' && typeof questionData.answer === 'string') {
            questionData.answer = questionData.answer.split(',');
          }

          this.forms = [questionData];
          this.batchCourseId = questionData.courseId;
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const processedForms = JSON.parse(JSON.stringify(this.forms));

          processedForms.forEach(form => {
            if (form.questionType === '1' && Array.isArray(form.answer)) {
              form.answer = form.answer.sort().join(',');
            }
            form.courseId = this.batchCourseId;
          });

          if (processedForms.length === 1 && processedForms[0].questionId != null) {
            updateBank(processedForms[0]).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改题目失败:', error);
              this.$modal.msgError("修改失败，请重试");
            });
          } else {
            if (!this.batchCourseId) {
              this.$modal.msgError("请选择所属课程");
              return;
            }
            addBank(processedForms).then(response => {
              // 处理响应，无论是直接的响应还是经过处理的数据
              const message = `成功新增 ${processedForms.length} 道题目`;
              this.$modal.msgSuccess(message);
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('添加题目失败:', error);
              // 检查是否是网络错误还是业务错误
              if (error.response && error.response.data && error.response.data.msg) {
                this.$modal.msgError(error.response.data.msg);
              } else {
                this.$modal.msgError("添加失败，请重试");
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const questionIds = row.questionId || this.ids;
      this.$modal.confirm('是否确认删除题库管理编号为"' + questionIds + '"的数据项？').then(() => {
        return delBank(questionIds);
      }).then(() => {
        this.getList();
        this.$modal.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('education/bank/export', {
        ...this.queryParams
      }, `bank_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "题库数据导入";
      this.upload.currentStep = 0;
      this.upload.courseId = null;
      this.upload.updateSupport = 0;
      this.upload.fileName = '';
      this.upload.result = null;
      this.upload.open = true;
    },
    /** 下一步 */
    nextStep() {
      if (this.upload.currentStep < 2) {
        this.upload.currentStep++;
      }
    },
    /** 上一步 */
    prevStep() {
      if (this.upload.currentStep > 0) {
        this.upload.currentStep--;
      }
    },
    /** 重置导入 */
    resetImport() {
      this.upload.currentStep = 0;
      this.upload.courseId = null;
      this.upload.updateSupport = 0;
      this.upload.fileName = '';
      this.upload.result = null;
      this.$refs.upload.clearFiles();
    },
    /** 完成导入 */
    finishImport() {
      this.upload.open = false;
      this.getList(); // 刷新列表
    },
    /** 文件变化处理 */
    handleFileChange(file, fileList) {
      if (file.raw) {
        this.upload.fileName = file.raw.name;
      }
    },
    /** 下载模板操作 */
    importTemplate() {
      downloadTemplate().then(response => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `题库导入模板_${new Date().getTime()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }).catch(error => {
        this.$message.error('模板下载失败');
        console.error('下载模板失败:', error);
      });
    },
    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;

      if (response.code === 200) {
        this.upload.result = response.data;
        this.upload.currentStep = 2;
        this.$message.success('导入完成！');
      } else {
        this.$message.error(response.msg || '导入失败');
      }
    },
    /** 文件上传失败处理 */
    handleFileError(err, file, fileList) {
      this.upload.isUploading = false;
      this.$message.error("上传失败，请重试");
    },
    /** 提交上传文件 */
    submitFileForm() {
      if (!this.upload.courseId) {
        this.$message.error("请选择所属课程");
        return;
      }
      this.$refs.upload.submit();
    },
    // 动态添加题目表单
    addQuestionForm() {
      this.forms.push(this.createNewQuestion());
    },
    // 动态移除题目表单
    removeQuestionForm(index) {
      if (this.forms.length > 1) {
        this.forms.splice(index, 1);
      } else {
        this.$modal.msgWarning("至少要保留一个题目");
      }
    },
    // 为指定题目添加选项
    addOption(formIndex) {
      this.forms[formIndex].options.push({ content: '' });
    },
    // 为指定题目移除选项
    removeOption(formIndex, optionIndex) {
      if (this.forms[formIndex].options.length > 2) {
        this.forms[formIndex].options.splice(optionIndex, 1);
      } else {
        this.$modal.msgError("选择题至少需要两个选项");
      }
    },
    // 题目类型改变时，处理答案和选项的格式
    handleTypeChange(type, formIndex) {
      const form = this.forms[formIndex];
      if (type === '1') { // 多选题
        form.answer = [];
      } else { // 单选、判断、简答
        form.answer = '';
      }

      if (type === '0' || type === '1') { // 单选或多选
        if (!form.options || form.options.length < 2) {
           form.options = [{ content: '' }, { content: '' }];
        }
      } else { // 判断题或简答题
        form.options = [];
      }
    }
  }
};
</script>

<style scoped>
.option-item {
  margin-bottom: 10px;
}
.box-card {
  margin-bottom: 15px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}

/* 导入对话框样式 */
.import-dialog >>> .el-dialog__body {
  padding: 20px;
}

.import-container {
  min-height: 400px;
}

.import-steps {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.card-header i {
  margin-right: 8px;
  font-size: 16px;
  color: #409EFF;
}

/* 配置区域样式 */
.config-card {
  border: none;
}

.config-form {
  margin-bottom: 20px;
}

.radio-option {
  display: block;
  margin-bottom: 15px;
  padding: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 8px;
  transition: all 0.3s;
  background: #fafafa;
}

.radio-option:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.radio-option >>> .el-radio__input.is-checked + .el-radio__label {
  color: #409EFF;
}

.radio-content {
  margin-left: 24px;
}

.radio-label {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.radio-label i {
  margin-right: 6px;
}

.radio-desc {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  line-height: 1.4;
}

.radio-note {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.radio-note i {
  margin-right: 4px;
  font-size: 12px;
}

.template-section {
  margin-top: 20px;
}

.template-download {
  text-align: center;
  margin-top: 15px;
}

.download-btn {
  padding: 12px 24px;
  font-size: 14px;
}

/* 上传区域样式 */
.upload-card {
  border: none;
}

.upload-dragger >>> .el-upload-dragger {
  width: 100%;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s;
}

.upload-dragger >>> .el-upload-dragger:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-title {
  font-size: 16px;
  color: #303133;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

.file-info {
  margin-top: 15px;
  text-align: center;
}

/* 结果区域样式 */
.result-card {
  border: none;
}

.result-summary {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-number.total {
  color: #909399;
}

.stat-number.success {
  color: #67C23A;
}

.stat-number.error {
  color: #F56C6C;
}

.stat-number.skip {
  color: #E6A23C;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.error-details, .warning-details {
  margin-top: 20px;
}

.error-list, .warning-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-item, .warning-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.error-item:last-child, .warning-item:last-child {
  border-bottom: none;
}

.error-message, .warning-message {
  margin-left: 10px;
  flex: 1;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
