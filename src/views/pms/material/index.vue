<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="材料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入材料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目" prop="projectId">
        <el-select v-model="queryParams.projectId" placeholder="请选择项目" clearable>
          <el-option
            v-for="project in projectList"
            :key="project.projectId"
            :label="project.projectName"
            :value="project.projectId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="despite">
        <el-input
          v-model="queryParams.despite"
          placeholder="请输入描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="尺寸" prop="size">
        <el-input
          v-model="queryParams.size"
          placeholder="请输入尺寸"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['pms:material:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['pms:material:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['pms:material:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-shopping-cart-2"
          size="mini"
          @click="handlePurchase"
        >生成采购单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="材料名称" align="center" prop="materialName" :show-overflow-tooltip="true" />
      <el-table-column label="单价" align="center" prop="unitPrice">
        <template slot-scope="scope">
          <span v-if="scope.row.unitPrice">¥{{ scope.row.unitPrice }}</span>
          <span v-else style="color: #ccc;">-</span>
        </template>
      </el-table-column>
      <el-table-column label="资源包" align="center" prop="resourceUrl" width="100">
        <template slot-scope="scope">
          <el-link v-if="scope.row.resourceUrl" :href="scope.row.resourceUrl" target="_blank" type="primary">
            <i class="el-icon-link"></i> 查看
          </el-link>
          <span v-else style="color: #ccc;">无资源</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="despite" :show-overflow-tooltip="true" />
      <el-table-column label="尺寸" align="center" prop="size" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['pms:material:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['pms:material:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改材料清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="材料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入材料名称" />
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="所属项目">
              <el-select v-model="form.projectId" placeholder="请选择项目（可选）" clearable>
                <el-option
                  v-for="project in projectList"
                  :key="project.projectId"
                  :label="project.projectName"
                  :value="project.projectId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number v-model="form.unitPrice" :min="0" :precision="2" placeholder="请输入单价" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="资源包" prop="resourceUrl">
          <el-upload
            ref="resourceUpload"
            :limit="1"
            :action="uploadFileUrl"
            :headers="uploadHeaders"
            :file-list="resourceFileList"
            :on-progress="handleResourceUploadProgress"
            :on-success="handleResourceUploadSuccess"
            :on-error="handleResourceUploadError"
            :before-upload="beforeResourceUpload"
            :on-exceed="handleExceed"
            :on-remove="handleResourceRemove"
            :show-file-list="true"
          >
            <el-button slot="trigger" size="small" type="primary">
              <i class="el-icon-upload"></i> 选择资源包
            </el-button>
            <div slot="tip" class="el-upload__tip">
              支持所有文件格式，大小不超过50MB
            </div>
          </el-upload>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="描述" prop="despite">
              <el-input v-model="form.despite" placeholder="请输入描述" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="尺寸" prop="size">
              <el-input v-model="form.size" placeholder="请输入尺寸" />
            </el-form-item>
          </el-col>
        </el-row>


        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog title="材料导入" :visible.sync="importOpen" width="400px" append-to-body :close-on-click-modal="false">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?projectId=' + selectedProjectId"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="importOpen = false">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial, saveMaterialFile } from "@/api/pms/material";
import { listProject } from "@/api/pms/project";
import { getToken } from "@/utils/auth";
;

export default {
  name: "Material",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 材料清单表格数据
      materialList: [],
      // 项目列表
      projectList: [],
      // 文件上传相关
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      resourceFileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 导入弹出层
      importOpen: false,
      // 选中的项目ID
      selectedProjectId: null,


      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialName: null,
        projectId: null,
        despite: null,
        size: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialName: [
          { required: true, message: "材料名称不能为空", trigger: "blur" }
        ]
      },
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/pms/material/importData"
      }
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    // 确保上传头部信息是最新的
    this.uploadHeaders = {
      Authorization: "Bearer " + getToken()
    };
  },
  methods: {
    /** 查询材料清单列表 */
    getList() {
      this.loading = true;
      listMaterial(this.queryParams).then(response => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取项目列表 */
    getProjectList() {
      listProject().then(response => {
        this.projectList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        materialId: null,
        materialName: null,
        projectId: null,
        unitPrice: null,
        resourceUrl: null,
        despite: null,
        size: null,
        remark: null
      };
      this.resourceFileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.materialId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加材料清单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const materialId = row.materialId || this.ids
      getMaterial(materialId).then(response => {
        this.form = response.data;
        // 设置资源包文件列表
        if (this.form.resourceUrl) {
          this.resourceFileList = [{
            name: '当前资源包',
            url: this.form.resourceUrl
          }];
        } else {
          this.resourceFileList = [];
        }
        this.open = true;
        this.title = "修改材料清单";
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      if (!this.selectedProjectId) {
        this.$modal.msgWarning("请先选择项目");
        return;
      }
      this.importOpen = true;
    },
    /** 采购单按钮操作 */
    handlePurchase() {
      const materialIds = this.ids;
      if (materialIds.length === 0) {
        this.$modal.msgWarning("请选择要生成采购单的材料");
        return;
      }
      // 生成采购单逻辑
      this.$modal.msgSuccess("采购单生成成功");
    },
    /** 提交按钮 */
    submitForm() {
      console.log('提交表单，当前form:', this.form);

      this.$refs["form"].validate(valid => {
        console.log('表单验证结果:', valid);

        if (valid) {
          if (this.form.materialId != null) {
            updateMaterial(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaterial(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        } else {
          console.log('表单验证失败');
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const materialIds = row.materialId || this.ids;
      this.$modal.confirm('是否确认删除材料编号为"' + materialIds + '"的数据项？').then(function() {
        return delMaterial(materialIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('pms/material/export', {
        ...this.queryParams
      }, `material_${new Date().getTime()}.xlsx`)
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('pms/material/importTemplate', {}, `material_template_${new Date().getTime()}.xlsx`)
    },
    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.importOpen = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    /** 提交上传文件 */
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 计算总价 */
    calculateTotal() {
      if (this.form.quantity && this.form.unitPrice) {
        this.form.totalPrice = (this.form.quantity * this.form.unitPrice).toFixed(2);
      }
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "upload":
          this.handleFileUpload(row);
          break;
        case "ordered":
          this.updateStatus(row.materialId, "ORDERED");
          break;
        case "received":
          this.updateStatus(row.materialId, "RECEIVED");
          break;
        case "used":
          this.updateStatus(row.materialId, "USED");
          break;
      }
    },
    /** 更新材料状态 */
    updateStatus(materialId, status) {
      updateMaterialStatus(materialId, status).then(response => {
        this.$modal.msgSuccess("状态更新成功");
        this.getList();
      });
    },

    // ==================== 文件上传处理方法 ====================

    /** 文件上传前检查 */
    beforeFileUpload(file) {
      const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
      const isLt50M = file.size / 1024 / 1024 < 50;

      if (!isValidType) {
        this.$message.error('文件格式不正确，请上传pdf、doc、docx、xls、xlsx、jpg、jpeg、png格式文件!');
        return false;
      }
      if (!isLt50M) {
        this.$message.error('文件大小不能超过50MB!');
        return false;
      }
      return true;
    },








    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    // ==================== 文件上传相关方法 ====================

    /** 资源包上传前检查 */
    beforeResourceUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50;

      if (!isLt50M) {
        this.$modal.msgError('文件大小不能超过 50MB!');
        return false;
      }
      return true;
    },

    /** 资源包上传进度 */
    handleResourceUploadProgress(event, file, fileList) {
      this.uploadProgress = Math.round(event.percent);
    },

    /** 资源包上传成功 */
    handleResourceUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.form.resourceUrl = response.url;
        this.$modal.msgSuccess('资源包上传成功');
      } else {
        this.$modal.msgError(response.msg || '资源包上传失败');
        this.resourceFileList = [];
      }
    },

    /** 资源包上传失败 */
    handleResourceUploadError(err, file, fileList) {
      this.$modal.msgError('资源包上传失败，请重试');
      this.resourceFileList = [];
    },

    /** 文件超出个数限制 */
    handleExceed(files, fileList) {
      this.$modal.msgError('最多只能上传1个资源包文件');
    },

    /** 移除资源包文件 */
    handleResourceRemove(file, fileList) {
      this.form.resourceUrl = null;
      this.resourceFileList = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.purchase-summary {
  margin-bottom: 15px;
}

.editor-container {
  height: 500px;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.editor-placeholder {
  text-align: center;
  color: #999;
}

.editor-placeholder p {
  margin: 10px 0;
  font-size: 14px;
}

.upload-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.upload-info p {
  margin: 5px 0;
  color: #606266;
}

.file-info {
  margin-top: 10px;
  padding: 8px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}
</style>
