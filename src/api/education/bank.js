import request from '@/utils/request'

// 查询题库管理列表
export function listBank(query) {
  return request({
    url: '/education/bank/list',
    method: 'get',
    params: query
  })
}

// 查询题库管理详细
export function getBank(questionId) {
  return request({
    url: '/education/bank/' + questionId,
    method: 'get'
  })
}

// 新增题库管理
export function addBank(data) {
  return request({
    url: '/education/bank',
    method: 'post',
    data: data
  })
}

// 修改题库管理
export function updateBank(data) {
  return request({
    url: '/education/bank',
    method: 'put',
    data: data
  })
}

// 删除题库管理
export function delBank(questionId) {
  return request({
    url: '/education/bank/' + questionId,
    method: 'delete'
  })
}

// 导出题库
export function exportBank(query) {
  return request({
    url: '/education/bank/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 批量导入题目
export function importBank(data) {
  return request({
    url: '/education/bank/import',
    method: 'post',
    data: data
  })
}

// 获取题目统计
export function getBankStatistics(questionId) {
  return request({
    url: `/education/bank/${questionId}/statistics`,
    method: 'get'
  })
}

// 获取题目使用记录
export function getQuestionUsage(questionId) {
  return request({
    url: `/education/bank/${questionId}/usage`,
    method: 'get'
  })
}

// 批量删除题目
export function batchDelBank(questionIds) {
  return request({
    url: '/education/bank/batch',
    method: 'delete',
    data: questionIds
  })
}

// 复制题目
export function copyQuestion(questionId) {
  return request({
    url: `/education/bank/${questionId}/copy`,
    method: 'post'
  })
}

// 获取题目分类统计
export function getBankCategoryStats() {
  return request({
    url: '/education/bank/categoryStats',
    method: 'get'
  })
}

// 下载导入模板
export function downloadTemplate() {
  return request({
    url: '/education/bank/importTemplate',
    method: 'get',
    responseType: 'blob'
  })
}
