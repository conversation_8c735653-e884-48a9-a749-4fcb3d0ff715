# 材料清单资源包上传功能实现

## 🎯 功能需求
将材料清单中的"资源包URL"字段从手动输入改为文件上传功能，支持上传压缩包文件。

## ✅ 实现的功能

### 1. 前端表单更新

#### 原来的输入框
```vue
<el-form-item label="资源包URL" prop="resourceUrl">
  <el-input v-model="form.resourceUrl" placeholder="请输入资源包URL" />
</el-form-item>
```

#### 更新后的文件上传组件
```vue
<el-form-item label="资源包" prop="resourceUrl">
  <el-upload
    ref="resourceUpload"
    :limit="1"
    accept=".zip,.rar,.7z,.tar,.gz"
    :action="uploadFileUrl"
    :headers="uploadHeaders"
    :file-list="resourceFileList"
    :on-progress="handleResourceUploadProgress"
    :on-success="handleResourceUploadSuccess"
    :on-error="handleResourceUploadError"
    :before-upload="beforeResourceUpload"
    :on-exceed="handleExceed"
    :on-remove="handleResourceRemove"
    :show-file-list="true"
  >
    <el-button slot="trigger" size="small" type="primary">
      <i class="el-icon-upload"></i> 选择资源包
    </el-button>
    <div slot="tip" class="el-upload__tip">
      支持zip、rar、7z、tar、gz等压缩格式，大小不超过50MB
    </div>
  </el-upload>
</el-form-item>
```

### 2. 支持的文件格式

#### 文件类型限制
- ✅ **ZIP格式**：.zip
- ✅ **RAR格式**：.rar  
- ✅ **7Z格式**：.7z
- ✅ **TAR格式**：.tar
- ✅ **GZIP格式**：.gz

#### 文件大小限制
- **最大大小**：50MB
- **数量限制**：每次只能上传1个文件

### 3. 新增的数据字段

```javascript
data() {
  return {
    // 文件上传相关
    uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload",
    uploadHeaders: {
      Authorization: "Bearer " + getToken()
    },
    resourceFileList: [],
    // ... 其他数据
  }
}
```

### 4. 新增的处理方法

#### 文件上传前检查
```javascript
beforeResourceUpload(file) {
  const isValidType = ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip'].includes(file.type) ||
                     file.name.toLowerCase().endsWith('.zip') ||
                     file.name.toLowerCase().endsWith('.rar') ||
                     file.name.toLowerCase().endsWith('.7z') ||
                     file.name.toLowerCase().endsWith('.tar') ||
                     file.name.toLowerCase().endsWith('.gz');
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isValidType) {
    this.$modal.msgError('资源包只能是 zip、rar、7z、tar、gz 格式!');
    return false;
  }
  if (!isLt50M) {
    this.$modal.msgError('资源包大小不能超过 50MB!');
    return false;
  }
  return true;
}
```

#### 上传成功处理
```javascript
handleResourceUploadSuccess(response, file, fileList) {
  if (response.code === 200) {
    this.form.resourceUrl = response.url;
    this.$modal.msgSuccess('资源包上传成功');
  } else {
    this.$modal.msgError(response.msg || '资源包上传失败');
    this.resourceFileList = [];
  }
}
```

#### 文件移除处理
```javascript
handleResourceRemove(file, fileList) {
  this.form.resourceUrl = null;
  this.resourceFileList = [];
}
```

### 5. 编辑功能适配

#### 编辑时显示现有文件
```javascript
handleUpdate(row) {
  this.reset();
  const materialId = row.materialId || this.ids
  getMaterial(materialId).then(response => {
    this.form = response.data;
    // 设置资源包文件列表
    if (this.form.resourceUrl) {
      this.resourceFileList = [{
        name: '当前资源包',
        url: this.form.resourceUrl
      }];
    } else {
      this.resourceFileList = [];
    }
    this.open = true;
    this.title = "修改材料清单";
  });
}
```

#### 表单重置时清空文件列表
```javascript
reset() {
  this.form = {
    materialId: null,
    materialName: null,
    projectId: null,
    unitPrice: null,
    resourceUrl: null,
    despite: null,
    size: null,
    remark: null
  };
  this.resourceFileList = [];
  this.resetForm("form");
}
```

## 🎨 用户体验优化

### 1. 视觉设计
- **上传按钮**：带图标的主色调按钮
- **提示信息**：清晰的格式和大小限制说明
- **文件列表**：显示已上传文件的名称和状态

### 2. 交互反馈
- **上传进度**：显示文件上传进度
- **成功提示**：上传成功后的确认消息
- **错误处理**：格式错误、大小超限的友好提示
- **文件移除**：支持删除已上传的文件

### 3. 功能完整性
- **文件预览**：显示当前已上传的资源包
- **重新上传**：支持替换现有资源包
- **表单验证**：与其他字段的验证规则集成

## 🔧 技术实现

### 1. 上传接口
- **接口地址**：`/common/upload`
- **请求方式**：POST
- **认证方式**：Bearer Token
- **返回格式**：JSON

### 2. 文件存储
- **存储路径**：服务器指定的上传目录
- **URL生成**：返回可访问的文件URL
- **文件管理**：支持文件的增删改查

### 3. 数据流转
1. **用户选择文件** → 前端验证格式和大小
2. **文件上传** → 调用通用上传接口
3. **获取URL** → 服务器返回文件访问地址
4. **保存数据** → 将URL保存到数据库
5. **显示文件** → 在表单中显示已上传文件

## 🚀 使用流程

### 新增材料时
1. 点击"新增"按钮
2. 填写材料名称等基本信息
3. 点击"选择资源包"按钮
4. 选择压缩包文件上传
5. 等待上传完成
6. 点击"确定"保存材料信息

### 编辑材料时
1. 点击"修改"按钮
2. 查看当前已上传的资源包
3. 可以删除现有文件重新上传
4. 或保持现有文件不变
5. 点击"确定"保存修改

## 📋 注意事项

### 1. 文件安全
- 严格的文件类型检查
- 文件大小限制
- 服务器端的安全扫描

### 2. 用户体验
- 清晰的操作提示
- 友好的错误信息
- 合理的上传进度显示

### 3. 数据一致性
- 文件上传失败时的回滚处理
- 编辑时的文件状态管理
- 删除材料时的文件清理

这个实现提供了完整的资源包上传功能，用户体验友好，技术实现稳定可靠。
