<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pms.mapper.PmsMaterialMapper">

    <resultMap type="com.ruoyi.pms.domain.PmsMaterial" id="PmsMaterialResult">
        <result property="materialId"     column="material_id"     />
        <result property="projectId"      column="project_id"      />
        <result property="materialName"   column="material_name"   />
        <result property="unitPrice"      column="unit_price"      />
        <result property="resourceUrl"    column="resource_url"    />
        <result property="despite"        column="despite"         />
        <result property="size"           column="size"            />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
        <result property="projectName"    column="project_name"    />
    </resultMap>

    <sql id="selectPmsMaterialVo">
        select m.material_id, m.project_id, m.material_name, m.unit_price, m.resource_url,
               m.despite, m.size, m.create_by, m.create_time, m.update_by, m.update_time, m.remark,
               p.project_name
        from pms_material m
        left join pms_project p on m.project_id = p.project_id
    </sql>

    <select id="selectMaterialList" parameterType="com.ruoyi.pms.domain.PmsMaterial" resultMap="PmsMaterialResult">
        <include refid="selectPmsMaterialVo"/>
        <where>
            <if test="projectId != null "> and m.project_id = #{projectId}</if>
            <if test="materialName != null  and materialName != ''"> and m.material_name like concat('%', #{materialName}, '%')</if>
            <if test="despite != null  and despite != ''"> and m.despite like concat('%', #{despite}, '%')</if>
            <if test="size != null  and size != ''"> and m.size = #{size}</if>
        </where>
        order by m.create_time desc
    </select>

    <select id="selectMaterialByMaterialId" parameterType="Long" resultMap="PmsMaterialResult">
        <include refid="selectPmsMaterialVo"/>
        where m.material_id = #{materialId}
    </select>

    <select id="selectMaterialsByProjectId" parameterType="Long" resultMap="PmsMaterialResult">
        <include refid="selectPmsMaterialVo"/>
        where m.project_id = #{projectId}
        order by m.category, m.material_name
    </select>



    <insert id="insertMaterial" parameterType="com.ruoyi.pms.domain.PmsMaterial" useGeneratedKeys="true" keyProperty="materialId">
        insert into pms_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="materialName != null and materialName != ''">material_name,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="resourceUrl != null">resource_url,</if>
            <if test="despite != null">despite,</if>
            <if test="size != null">size,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="materialName != null and materialName != ''">#{materialName},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="resourceUrl != null">#{resourceUrl},</if>
            <if test="despite != null">#{despite},</if>
            <if test="size != null">#{size},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateMaterial" parameterType="com.ruoyi.pms.domain.PmsMaterial">
        update pms_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="materialName != null and materialName != ''">material_name = #{materialName},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="resourceUrl != null">resource_url = #{resourceUrl},</if>
            <if test="despite != null">despite = #{despite},</if>
            <if test="size != null">size = #{size},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where material_id = #{materialId}
    </update>

    <delete id="deleteMaterialByMaterialId" parameterType="Long">
        delete from pms_material where material_id = #{materialId}
    </delete>

    <delete id="deleteMaterialByMaterialIds" parameterType="String">
        delete from pms_material where material_id in
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </delete>

    <delete id="deleteMaterialsByProjectId" parameterType="Long">
        delete from pms_material where project_id = #{projectId}
    </delete>

    <insert id="batchInsertMaterials" parameterType="java.util.List">
        insert into pms_material (project_id, material_name, unit_price, resource_url, despite, size, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.materialName}, #{item.unitPrice}, #{item.resourceUrl}, #{item.despite}, #{item.size},
             #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <select id="countMaterials" parameterType="com.ruoyi.pms.domain.PmsMaterial" resultType="int">
        select count(*) from pms_material
        <where>
            <if test="projectId != null"> and project_id = #{projectId}</if>
            <if test="size != null and size != ''"> and size = #{size}</if>
            <if test="despite != null and despite != ''"> and despite like concat('%', #{despite}, '%')</if>
        </where>
    </select>

    <select id="countMaterialsByProject" parameterType="Long" resultType="int">
        select count(*) from pms_material where project_id = #{projectId}
    </select>

    <select id="sumMaterialValueByProject" parameterType="Long" resultType="Double">
        select sum(unit_price) from pms_material where project_id = #{projectId}
    </select>

</mapper>
