package com.ruoyi.pms.service;

import java.util.List;
import com.ruoyi.pms.domain.PmsMaterial;

/**
 * PMS材料清单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IPmsMaterialService 
{
    /**
     * 查询材料清单
     * 
     * @param materialId 材料清单主键
     * @return 材料清单
     */
    public PmsMaterial selectMaterialByMaterialId(Long materialId);

    /**
     * 查询材料清单列表
     * 
     * @param material 材料清单
     * @return 材料清单集合
     */
    public List<PmsMaterial> selectMaterialList(PmsMaterial material);

    /**
     * 根据项目ID查询材料列表
     * 
     * @param projectId 项目ID
     * @return 材料集合
     */
    public List<PmsMaterial> selectMaterialsByProjectId(Long projectId);

    /**
     * 新增材料清单
     *
     * @param material 材料清单
     * @return 结果
     */
    public int insertMaterial(PmsMaterial material);

    /**
     * 修改材料清单
     *
     * @param material 材料清单
     * @return 结果
     */
    public int updateMaterial(PmsMaterial material);

    /**
     * 批量删除材料清单
     *
     * @param materialIds 需要删除的材料清单主键集合
     * @return 结果
     */
    public int deleteMaterialByMaterialIds(Long[] materialIds);

    /**
     * 删除材料清单信息
     *
     * @param materialId 材料清单主键
     * @return 结果
     */
    public int deleteMaterialByMaterialId(Long materialId);

    /**
     * 批量导入材料
     * 
     * @param materials 材料列表
     * @param projectId 项目ID
     * @return 结果
     */
    public int importMaterials(List<PmsMaterial> materials, Long projectId);

    /**
     * 从3D编辑器同步材料清单
     * 
     * @param projectId 项目ID
     * @param editorData 3D编辑器数据
     * @return 结果
     */
    public int syncMaterialsFromEditor(Long projectId, Object editorData);

    /**
     * 获取材料统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息
     */
    public Object getMaterialStatistics(Long projectId);

    /**
     * 生成材料采购清单
     * 
     * @param projectId 项目ID
     * @return 采购清单
     */
    public Object generatePurchaseList(Long projectId);

    /**
     * 上传资源包文件
     *
     * @param materialId 材料ID
     * @param file 文件
     * @return 文件URL
     */
    public String uploadResourceFile(Long materialId, org.springframework.web.multipart.MultipartFile file) throws Exception;

    /**
     * 上传临时文件
     *
     * @param file 文件
     * @return 文件URL
     */
    public String uploadTempFile(org.springframework.web.multipart.MultipartFile file) throws Exception;
}
