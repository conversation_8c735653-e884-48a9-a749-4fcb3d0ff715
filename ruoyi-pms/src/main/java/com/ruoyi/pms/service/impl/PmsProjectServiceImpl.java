package com.ruoyi.pms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.pms.mapper.PmsProjectMapper;
import com.ruoyi.pms.domain.PmsProject;
import com.ruoyi.pms.domain.PmsTask;
import com.ruoyi.pms.domain.PmsProjectMember;
import com.ruoyi.pms.domain.PmsMaterial;
import com.ruoyi.pms.service.IPmsProjectService;
import com.ruoyi.pms.service.IPmsTaskService;
import com.ruoyi.pms.service.IPmsProjectMemberService;
import com.ruoyi.pms.service.IPmsMaterialService;

/**
 * PMS项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class PmsProjectServiceImpl implements IPmsProjectService
{
    @Autowired
    private PmsProjectMapper pmsProjectMapper;

    @Autowired
    private IPmsTaskService pmsTaskService;

    @Autowired
    private IPmsProjectMemberService pmsProjectMemberService;

    @Autowired
    private IPmsMaterialService pmsMaterialService;

    /**
     * 查询项目
     *
     * @param projectId 项目主键
     * @return 项目
     */
    @Override
    public PmsProject selectProjectByProjectId(Long projectId)
    {
        return pmsProjectMapper.selectProjectByProjectId(projectId);
    }

    /**
     * 查询项目列表
     *
     * @param project 项目
     * @return 项目
     */
    @Override
    public List<PmsProject> selectProjectList(PmsProject project)
    {
        return pmsProjectMapper.selectProjectList(project);
    }

    /**
     * 查询教师相关的项目列表
     *
     * @param teacherId 教师ID
     * @return 项目集合
     */
    @Override
    public List<PmsProject> selectProjectsByTeacherId(Long teacherId)
    {
        return pmsProjectMapper.selectProjectsByTeacherId(teacherId);
    }

    /**
     * 查询学生相关的项目列表
     *
     * @param studentId 学生ID
     * @return 项目集合
     */
    @Override
    public List<PmsProject> selectProjectsByStudentId(Long studentId)
    {
        return pmsProjectMapper.selectProjectsByStudentId(studentId);
    }

    /**
     * 新增项目
     *
     * @param project 项目
     * @return 结果
     */
    @Override
    public int insertProject(PmsProject project)
    {
        project.setCreateTime(DateUtils.getNowDate());

        // 处理多选负责人ID - 如果前端传来的是数组，需要转换为逗号分隔的字符串
        // 这个处理逻辑会在Controller层完成

        return pmsProjectMapper.insertProject(project);
    }

    /**
     * 修改项目
     *
     * @param project 项目
     * @return 结果
     */
    @Override
    public int updateProject(PmsProject project)
    {
        project.setUpdateTime(DateUtils.getNowDate());
        return pmsProjectMapper.updateProject(project);
    }

    /**
     * 批量删除项目
     *
     * @param projectIds 需要删除的项目主键
     * @return 结果
     */
    @Override
    public int deleteProjectByProjectIds(Long[] projectIds)
    {
        return pmsProjectMapper.deleteProjectByProjectIds(projectIds);
    }

    /**
     * 删除项目信息
     *
     * @param projectId 项目主键
     * @return 结果
     */
    @Override
    public int deleteProjectByProjectId(Long projectId)
    {
        return pmsProjectMapper.deleteProjectByProjectId(projectId);
    }

    /**
     * 检查项目编号是否唯一
     *
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public boolean checkProjectCodeUnique(PmsProject project)
    {
        Long projectId = StringUtils.isNull(project.getProjectId()) ? -1L : project.getProjectId();
        PmsProject info = pmsProjectMapper.selectProjectByCode(project.getProjectCode());
        if (StringUtils.isNotNull(info) && info.getProjectId().longValue() != projectId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 指派项目
     *
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public int assignProject(PmsProject project)
    {
        project.setUpdateTime(DateUtils.getNowDate());
        return pmsProjectMapper.updateProject(project);
    }

    /**
     * 更新项目进度
     *
     * @param projectId 项目ID
     * @param progress 进度
     * @return 结果
     */
    @Override
    public int updateProjectProgress(Long projectId, Long progress)
    {
        PmsProject project = new PmsProject();
        project.setProjectId(projectId);
        project.setProgress(progress.intValue()); // 转换为Integer
        project.setUpdateBy(SecurityUtils.getUsername());
        project.setUpdateTime(DateUtils.getNowDate());

        // 如果进度达到100%，自动设置实际结束时间
        if (progress >= 100) {
            project.setActualEndDate(DateUtils.getNowDate());
            project.setProjectStatus("COMPLETED"); // 项目完成
        }

        return pmsProjectMapper.updateProject(project);
    }

    /**
     * 获取项目统计信息
     *
     * @return 统计信息
     */
    @Override
    public Object getProjectStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();

        // 项目总数
        int totalCount = pmsProjectMapper.countProjects(new PmsProject());
        statistics.put("totalCount", totalCount);

        // 各状态项目数量
        statistics.put("planningCount", pmsProjectMapper.countProjectsByStatus("PLANNING"));
        statistics.put("inProgressCount", pmsProjectMapper.countProjectsByStatus("IN_PROGRESS"));
        statistics.put("completedCount", pmsProjectMapper.countProjectsByStatus("COMPLETED"));
        statistics.put("pausedCount", pmsProjectMapper.countProjectsByStatus("PAUSED"));
        statistics.put("cancelledCount", pmsProjectMapper.countProjectsByStatus("CANCELLED"));

        // 项目统计信息
        List<PmsProject> projectStats = pmsProjectMapper.getProjectStatistics();
        statistics.put("projectStats", projectStats);

        return statistics;
    }

    /**
     * 获取即将到期的项目
     *
     * @param days 天数
     * @return 项目列表
     */
    @Override
    public List<PmsProject> getProjectsNearDeadline(int days)
    {
        return pmsProjectMapper.getProjectsNearDeadline(days);
    }

    @Override
    public void initializeProjectExecution(Long projectId) {
        // 这里可以初始化项目执行相关的数据
        // 例如：创建默认任务、初始化甘特图数据等

        // 1. 创建项目阶段任务
        createDefaultProjectTasks(projectId);

        // 2. 初始化项目成员（如果还没有的话）
        initializeProjectMembers(projectId);

    }

    private void createDefaultProjectTasks(Long projectId) {
        // 创建默认的项目阶段任务
        PmsProject project = pmsProjectMapper.selectProjectByProjectId(projectId);
        if (project == null) return;

        // 根据项目类型创建不同的默认任务
        String[] defaultTasks = getDefaultTasksByProjectType(project.getProjectType());

        for (int i = 0; i < defaultTasks.length; i++) {
            PmsTask task = new PmsTask();
            task.setProjectId(projectId);
            task.setTaskName(defaultTasks[i]);
            task.setTaskType("PHASE"); // 阶段任务
            task.setStatus("NOT_STARTED"); // 未开始
            task.setAssigneeId(String.valueOf(project.getLeaderId())); // 分配给项目负责人
            // 不再设置assigneeName，通过关联查询获取
            task.setPriority("MEDIUM"); // 中等优先级
            task.setProgress(0L); // 初始进度为0

            // 设置计划开始和结束时间
            Date startDate = DateUtils.addDays(new Date(), i * 7); // 每个任务间隔一周
            Date endDate = DateUtils.addDays(startDate, 6); // 每个任务持续一周
            task.setPlannedStartDate(startDate);
            task.setPlannedEndDate(endDate);

            task.setCreateBy(SecurityUtils.getUsername());
            task.setCreateTime(DateUtils.getNowDate());

            // 调用任务服务创建任务
            pmsTaskService.insertTask(task);
        }
    }

    private String[] getDefaultTasksByProjectType(String projectType) {
        if ("ASSIGNED".equals(projectType)) {
            return new String[]{
                "项目需求分析",
                "方案设计",
                "技术选型",
                "开发实施",
                "测试验收",
                "项目总结"
            };
        } else if ("PROPOSAL".equals(projectType)) {
            return new String[]{
                "方案调研",
                "可行性分析",
                "初步设计",
                "方案完善",
                "答辩准备"
            };
        }
        return new String[]{"项目启动", "项目实施", "项目验收"};
    }

    private void initializeProjectMembers(Long projectId) {
        // 初始化项目成员 - 添加项目负责人为成员
        PmsProject project = pmsProjectMapper.selectProjectByProjectId(projectId);
        if (project == null) return;

        // 检查项目负责人是否已经是成员
        PmsProjectMember existingMember = pmsProjectMemberService.checkProjectMember(projectId, project.getLeaderId());
        if (existingMember == null) {
            // 添加项目负责人为项目成员
            PmsProjectMember member = new PmsProjectMember();
            member.setProjectId(projectId);
            member.setUserId(project.getLeaderId());
            member.setUserName(project.getLeaderName());
            member.setUserType("STUDENT"); // 假设负责人是学生
            member.setRole("LEADER"); // 角色为负责人
            member.setJoinDate(DateUtils.getNowDate());
            member.setStatus("ACTIVE"); // 活跃状态
            member.setCreateBy(SecurityUtils.getUsername());
            member.setCreateTime(DateUtils.getNowDate());

            pmsProjectMemberService.insertProjectMember(member);
        }
    }


}
