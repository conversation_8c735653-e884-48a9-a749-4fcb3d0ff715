package com.ruoyi.pms.service.impl;

import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.File;
import java.io.IOException;
import java.util.UUID;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.pms.mapper.PmsMaterialMapper;
import com.ruoyi.pms.domain.PmsMaterial;
import com.ruoyi.pms.service.IPmsMaterialService;

/**
 * PMS材料清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class PmsMaterialServiceImpl implements IPmsMaterialService
{
    @Autowired
    private PmsMaterialMapper pmsMaterialMapper;

    /**
     * 查询材料清单
     *
     * @param materialId 材料清单主键
     * @return 材料清单
     */
    @Override
    public PmsMaterial selectMaterialByMaterialId(Long materialId)
    {
        return pmsMaterialMapper.selectMaterialByMaterialId(materialId);
    }

    /**
     * 查询材料清单列表
     *
     * @param material 材料清单
     * @return 材料清单
     */
    @Override
    public List<PmsMaterial> selectMaterialList(PmsMaterial material)
    {
        return pmsMaterialMapper.selectMaterialList(material);
    }

    /**
     * 根据项目ID查询材料列表
     *
     * @param projectId 项目ID
     * @return 材料集合
     */
    @Override
    public List<PmsMaterial> selectMaterialsByProjectId(Long projectId)
    {
        return pmsMaterialMapper.selectMaterialsByProjectId(projectId);
    }



    /**
     * 新增材料清单
     *
     * @param material 材料清单
     * @return 结果
     */
    @Override
    public int insertMaterial(PmsMaterial material)
    {
        material.setCreateTime(DateUtils.getNowDate());
        return pmsMaterialMapper.insertMaterial(material);
    }

    /**
     * 修改材料清单
     *
     * @param material 材料清单
     * @return 结果
     */
    @Override
    public int updateMaterial(PmsMaterial material)
    {
        material.setUpdateTime(DateUtils.getNowDate());
        return pmsMaterialMapper.updateMaterial(material);
    }

    /**
     * 批量删除材料清单
     *
     * @param materialIds 需要删除的材料清单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialByMaterialIds(Long[] materialIds)
    {
        return pmsMaterialMapper.deleteMaterialByMaterialIds(materialIds);
    }

    /**
     * 删除材料清单信息
     *
     * @param materialId 材料清单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialByMaterialId(Long materialId)
    {
        return pmsMaterialMapper.deleteMaterialByMaterialId(materialId);
    }



    /**
     * 批量导入材料
     *
     * @param materials 材料列表
     * @param projectId 项目ID
     * @return 结果
     */
    @Override
    public int importMaterials(List<PmsMaterial> materials, Long projectId)
    {
        if (StringUtils.isNull(materials) || materials.size() == 0)
        {
            return 0;
        }

        for (PmsMaterial material : materials)
        {
            material.setProjectId(projectId);
            material.setCreateTime(DateUtils.getNowDate());
        }

        return pmsMaterialMapper.batchInsertMaterials(materials);
    }

    /**
     * 从3D编辑器同步材料清单
     *
     * @param projectId 项目ID
     * @param editorData 3D编辑器数据
     * @return 结果
     */
    @Override
    @SuppressWarnings("unchecked")
    public int syncMaterialsFromEditor(Long projectId, Object editorData)
    {
        try {
            if (editorData instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) editorData;
                List<Map<String, Object>> materials = (List<Map<String, Object>>) data.get("materials");

                if (materials != null && !materials.isEmpty()) {
                    int syncCount = 0;
                    for (Map<String, Object> materialData : materials) {
                        PmsMaterial material = new PmsMaterial();
                        material.setProjectId(projectId);
                        material.setMaterialName((String) materialData.get("materialName"));
                        material.setResourceUrl((String) materialData.get("resourceUrl"));
                        material.setDespite((String) materialData.get("despite"));
                        material.setSize((String) materialData.get("size"));
                        material.setCreateTime(DateUtils.getNowDate());

                        // 直接插入材料（简化逻辑）
                        pmsMaterialMapper.insertMaterial(material);
                        syncCount++;
                    }
                    return syncCount;
                }
            }
        } catch (Exception e) {
            // 记录错误日志
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 获取材料统计信息
     *
     * @param projectId 项目ID
     * @return 统计信息
     */
    @Override
    public Object getMaterialStatistics(Long projectId)
    {
        Map<String, Object> statistics = new HashMap<>();

        // 查询项目所有材料
        PmsMaterial queryMaterial = new PmsMaterial();
        queryMaterial.setProjectId(projectId);
        List<PmsMaterial> allMaterials = pmsMaterialMapper.selectMaterialList(queryMaterial);

        // 材料总数
        statistics.put("totalCount", allMaterials.size());

        // 材料总价值
        double totalValue = allMaterials.stream()
                .mapToDouble(m -> m.getUnitPrice() != null ? m.getUnitPrice().doubleValue() : 0.0)
                .sum();
        statistics.put("totalValue", totalValue);

        // 尺寸统计
        Map<String, Long> sizeCount = allMaterials.stream()
                .collect(Collectors.groupingBy(
                    m -> m.getSize() != null ? m.getSize() : "未知尺寸",
                    Collectors.counting()
                ));
        statistics.put("sizeCount", sizeCount.size());
        statistics.put("sizeStats", sizeCount);

        return statistics;
    }

    /**
     * 生成材料采购清单
     *
     * @param projectId 项目ID
     * @return 采购清单
     */
    @Override
    public Object generatePurchaseList(Long projectId)
    {
        // 查询项目所有材料
        PmsMaterial queryMaterial = new PmsMaterial();
        queryMaterial.setProjectId(projectId);

        List<PmsMaterial> materials = pmsMaterialMapper.selectMaterialList(queryMaterial);

        Map<String, Object> purchaseList = new HashMap<>();
        purchaseList.put("materials", materials);
        purchaseList.put("totalCount", materials.size());

        // 计算总价值
        double totalValue = materials.stream()
                .mapToDouble(m -> m.getUnitPrice() != null ? m.getUnitPrice().doubleValue() : 0.0)
                .sum();
        purchaseList.put("totalValue", totalValue);

        return purchaseList;
    }



    /**
     * 上传资源包文件
     *
     * @param materialId 材料ID
     * @param file 文件
     * @return 文件URL
     */
    @Override
    public String uploadResourceFile(Long materialId, MultipartFile file) throws Exception
    {
        if (file.isEmpty()) {
            throw new Exception("上传文件不能为空");
        }

        // 查询材料信息
        PmsMaterial material = pmsMaterialMapper.selectMaterialByMaterialId(materialId);
        if (material == null) {
            throw new Exception("材料不存在");
        }

        // 获取文件扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成新的文件名
        String fileName = "material_" + materialId + "_" + UUID.randomUUID().toString() + extension;

        // 设置上传路径（这里使用相对路径，实际部署时需要配置绝对路径）
        String uploadPath = System.getProperty("user.dir") + File.separator + "upload" + File.separator + "materials";
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 保存文件
        File destFile = new File(uploadDir, fileName);
        try {
            file.transferTo(destFile);
        } catch (IOException e) {
            throw new Exception("文件保存失败: " + e.getMessage());
        }

        // 生成文件URL
        String resourceUrl = "/upload/materials/" + fileName;

        // 更新材料的资源包URL
        material.setResourceUrl(resourceUrl);
        material.setUpdateTime(DateUtils.getNowDate());
        pmsMaterialMapper.updateMaterial(material);

        return resourceUrl;
    }

    /**
     * 上传临时文件
     *
     * @param file 文件
     * @return 文件URL
     */
    @Override
    public String uploadTempFile(MultipartFile file) throws Exception
    {
        if (file.isEmpty()) {
            throw new Exception("上传文件不能为空");
        }

        // 获取文件扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成新的文件名
        String fileName = "temp_" + UUID.randomUUID().toString() + extension;

        // 设置上传路径
        String uploadPath = System.getProperty("user.dir") + File.separator + "upload" + File.separator + "materials";
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 保存文件
        File destFile = new File(uploadDir, fileName);
        try {
            file.transferTo(destFile);
        } catch (IOException e) {
            throw new Exception("文件保存失败: " + e.getMessage());
        }

        // 生成文件URL
        String fileUrl = "/upload/materials/" + fileName;

        return fileUrl;
    }
}
