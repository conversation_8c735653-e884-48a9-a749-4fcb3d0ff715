package com.ruoyi.pms.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.pms.domain.*;
import com.ruoyi.pms.mapper.*;
import com.ruoyi.pms.service.IPmsAnalysisService;
import com.ruoyi.common.utils.DateUtils;

/**
 * PMS数据分析服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class PmsAnalysisServiceImpl implements IPmsAnalysisService
{
    @Autowired
    private PmsProjectMapper projectMapper;

    @Autowired
    private PmsTaskMapper taskMapper;

    @Autowired
    private PmsWorkLogMapper workLogMapper;

    @Autowired
    private PmsMaterialMapper materialMapper;

    @Autowired
    private PmsProjectMemberMapper memberMapper;

    /**
     * 获取项目统计概览
     */
    @Override
    public Map<String, Object> getProjectOverview()
    {
        Map<String, Object> result = new HashMap<>();

        // 获取所有项目
        List<PmsProject> allProjects = projectMapper.selectProjectList(new PmsProject());

        // 统计项目状态
        Map<String, Long> statusCount = allProjects.stream()
            .collect(Collectors.groupingBy(PmsProject::getProjectStatus, Collectors.counting()));

        // 统计项目类型
        Map<String, Long> typeCount = allProjects.stream()
            .collect(Collectors.groupingBy(PmsProject::getProjectType, Collectors.counting()));

        // 计算总预算和实际成本
        BigDecimal totalBudget = allProjects.stream()
            .filter(p -> p.getBudget() != null)
            .map(PmsProject::getBudget)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalActualCost = allProjects.stream()
            .filter(p -> p.getActualCost() != null)
            .map(PmsProject::getActualCost)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算平均进度
        double avgProgress = allProjects.stream()
            .mapToInt(p -> p.getProgress() != null ? p.getProgress() : 0)
            .average()
            .orElse(0.0);

        result.put("totalProjects", allProjects.size());
        result.put("statusDistribution", statusCount);
        result.put("typeDistribution", typeCount);
        result.put("totalBudget", totalBudget);
        result.put("totalActualCost", totalActualCost);
        result.put("averageProgress", Math.round(avgProgress));
        result.put("budgetUtilization", totalBudget.compareTo(BigDecimal.ZERO) > 0 ?
            totalActualCost.divide(totalBudget, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) : BigDecimal.ZERO);

        return result;
    }

    /**
     * 获取工时分析报告
     */
    @Override
    public Map<String, Object> getWorkHourAnalysis(Long projectId)
    {
        Map<String, Object> result = new HashMap<>();

        // 构建查询条件
        PmsTask taskQuery = new PmsTask();
        if (projectId != null) {
            taskQuery.setProjectId(projectId);
        }

        List<PmsTask> tasks = taskMapper.selectTaskList(taskQuery);

        // 计算总预估工时和实际工时
        BigDecimal totalEstimatedHours = tasks.stream()
            .filter(t -> t.getEstimatedHours() != null)
            .map(PmsTask::getEstimatedHours)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalActualHours = tasks.stream()
            .filter(t -> t.getActualHours() != null)
            .map(PmsTask::getActualHours)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算工时偏差
        BigDecimal hourDeviation = totalActualHours.subtract(totalEstimatedHours);
        BigDecimal deviationPercentage = totalEstimatedHours.compareTo(BigDecimal.ZERO) > 0 ?
            hourDeviation.divide(totalEstimatedHours, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) : BigDecimal.ZERO;

        // 按人员统计工时
        Map<String, BigDecimal> hoursByPerson = tasks.stream()
            .filter(t -> t.getAssigneeNames() != null && t.getActualHours() != null)
            .collect(Collectors.groupingBy(
                PmsTask::getAssigneeNames,
                Collectors.reducing(BigDecimal.ZERO, PmsTask::getActualHours, BigDecimal::add)
            ));

        // 按月份统计工时趋势
        Map<String, BigDecimal> monthlyHours = getMonthlyWorkHours(projectId);

        result.put("totalEstimatedHours", totalEstimatedHours);
        result.put("totalActualHours", totalActualHours);
        result.put("hourDeviation", hourDeviation);
        result.put("deviationPercentage", deviationPercentage);
        result.put("hoursByPerson", hoursByPerson);
        result.put("monthlyTrend", monthlyHours);

        return result;
    }

    /**
     * 获取人员产值分析
     */
    @Override
    public Map<String, Object> getPersonProductivityAnalysis(String startDate, String endDate)
    {
        Map<String, Object> result = new HashMap<>();

        // 获取时间范围内的工作日志
        PmsWorkLog logQuery = new PmsWorkLog();
        List<PmsWorkLog> workLogs = workLogMapper.selectWorkLogList(logQuery);

        // 按人员统计工作量
        Map<String, Map<String, Object>> personStats = new HashMap<>();

        for (PmsWorkLog log : workLogs) {
            String userName = log.getUserName();
            personStats.computeIfAbsent(userName, k -> new HashMap<>());
            Map<String, Object> stats = personStats.get(userName);

            // 累计工作时长
            BigDecimal currentHours = (BigDecimal) stats.getOrDefault("totalHours", BigDecimal.ZERO);
            BigDecimal logHours = log.getWorkHours() != null ? log.getWorkHours() : BigDecimal.ZERO;
            stats.put("totalHours", currentHours.add(logHours));

            // 累计日志数量
            Integer logCount = (Integer) stats.getOrDefault("logCount", 0);
            stats.put("logCount", logCount + 1);

            // 计算平均每日工时
            if (logCount > 0) {
                BigDecimal avgDailyHours = currentHours.add(logHours).divide(new BigDecimal(logCount + 1), 2, RoundingMode.HALF_UP);
                stats.put("avgDailyHours", avgDailyHours);
            }
        }

        // 计算产值排名
        List<Map<String, Object>> productivityRanking = personStats.entrySet().stream()
            .map(entry -> {
                Map<String, Object> item = new HashMap<>();
                item.put("userName", entry.getKey());
                item.putAll(entry.getValue());
                return item;
            })
            .sorted((a, b) -> ((BigDecimal) b.get("totalHours")).compareTo((BigDecimal) a.get("totalHours")))
            .collect(Collectors.toList());

        result.put("personStats", personStats);
        result.put("productivityRanking", productivityRanking);
        result.put("totalPersons", personStats.size());

        return result;
    }

    /**
     * 获取项目进度分析
     */
    @Override
    public Map<String, Object> getProjectProgressAnalysis()
    {
        Map<String, Object> result = new HashMap<>();

        List<PmsProject> projects = projectMapper.selectProjectList(new PmsProject());

        // 按进度区间分组
        Map<String, Long> progressDistribution = new HashMap<>();
        progressDistribution.put("0-25%", 0L);
        progressDistribution.put("26-50%", 0L);
        progressDistribution.put("51-75%", 0L);
        progressDistribution.put("76-100%", 0L);

        for (PmsProject project : projects) {
            int progress = project.getProgress() != null ? project.getProgress() : 0;
            if (progress <= 25) {
                progressDistribution.put("0-25%", progressDistribution.get("0-25%") + 1);
            } else if (progress <= 50) {
                progressDistribution.put("26-50%", progressDistribution.get("26-50%") + 1);
            } else if (progress <= 75) {
                progressDistribution.put("51-75%", progressDistribution.get("51-75%") + 1);
            } else {
                progressDistribution.put("76-100%", progressDistribution.get("76-100%") + 1);
            }
        }

        // 计算延期项目
        Date now = new Date();
        long delayedProjects = projects.stream()
            .filter(p -> p.getEndDate() != null && p.getEndDate().before(now))
            .filter(p -> !"COMPLETED".equals(p.getProjectStatus()))
            .count();

        // 计算按时完成率
        long completedProjects = projects.stream()
            .filter(p -> "COMPLETED".equals(p.getProjectStatus()))
            .count();

        long onTimeCompleted = projects.stream()
            .filter(p -> "COMPLETED".equals(p.getProjectStatus()))
            .filter(p -> p.getActualEndDate() != null && p.getEndDate() != null)
            .filter(p -> !p.getActualEndDate().after(p.getEndDate()))
            .count();

        double onTimeRate = completedProjects > 0 ? (double) onTimeCompleted / completedProjects * 100 : 0;

        result.put("progressDistribution", progressDistribution);
        result.put("delayedProjects", delayedProjects);
        result.put("onTimeCompletionRate", Math.round(onTimeRate));
        result.put("totalProjects", projects.size());
        result.put("completedProjects", completedProjects);

        return result;
    }

    /**
     * 获取材料成本分析
     */
    @Override
    public Map<String, Object> getMaterialCostAnalysis(Long projectId)
    {
        Map<String, Object> result = new HashMap<>();

        PmsMaterial materialQuery = new PmsMaterial();
        if (projectId != null) {
            materialQuery.setProjectId(projectId);
        }

        List<PmsMaterial> materials = materialMapper.selectMaterialList(materialQuery);




        return result;
    }

    /**
     * 获取学生项目参与度分析
     */
    @Override
    public Map<String, Object> getStudentParticipationAnalysis(Long classId)
    {
        Map<String, Object> result = new HashMap<>();

        // 获取项目成员信息
        PmsProjectMember memberQuery = new PmsProjectMember();
        memberQuery.setUserType("STUDENT");
        List<PmsProjectMember> members = memberMapper.selectProjectMemberList(memberQuery);

        // 按学生统计参与项目数
        Map<String, Long> participationCount = members.stream()
            .collect(Collectors.groupingBy(PmsProjectMember::getUserName, Collectors.counting()));

        // 计算平均参与度
        double avgParticipation = participationCount.values().stream()
            .mapToLong(Long::longValue)
            .average()
            .orElse(0.0);

        // 获取活跃学生排名
        List<Map<String, Object>> activeStudents = participationCount.entrySet().stream()
            .map(entry -> {
                Map<String, Object> student = new HashMap<>();
                student.put("studentName", entry.getKey());
                student.put("projectCount", entry.getValue());
                return student;
            })
            .sorted((a, b) -> ((Long) b.get("projectCount")).compareTo((Long) a.get("projectCount")))
            .limit(20)
            .collect(Collectors.toList());

        result.put("participationCount", participationCount);
        result.put("averageParticipation", Math.round(avgParticipation));
        result.put("activeStudents", activeStudents);
        result.put("totalStudents", participationCount.size());

        return result;
    }

    /**
     * 获取教师指导效果分析
     */
    @Override
    public Map<String, Object> getTeacherGuidanceAnalysis(Long teacherId)
    {
        Map<String, Object> result = new HashMap<>();

        PmsProject projectQuery = new PmsProject();
        if (teacherId != null) {
            projectQuery.setTeacherId(teacherId);
        }

        List<PmsProject> projects = projectMapper.selectProjectList(projectQuery);

        // 按教师统计指导项目
        Map<String, List<PmsProject>> projectsByTeacher = projects.stream()
            .filter(p -> p.getTeacherName() != null)
            .collect(Collectors.groupingBy(PmsProject::getTeacherName));

        Map<String, Map<String, Object>> teacherStats = new HashMap<>();

        for (Map.Entry<String, List<PmsProject>> entry : projectsByTeacher.entrySet()) {
            String teacherName = entry.getKey();
            List<PmsProject> teacherProjects = entry.getValue();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalProjects", teacherProjects.size());

            // 计算完成率
            long completedCount = teacherProjects.stream()
                .filter(p -> "COMPLETED".equals(p.getProjectStatus()))
                .count();
            double completionRate = teacherProjects.size() > 0 ?
                (double) completedCount / teacherProjects.size() * 100 : 0;
            stats.put("completionRate", Math.round(completionRate));

            // 计算平均进度
            double avgProgress = teacherProjects.stream()
                .mapToInt(p -> p.getProgress() != null ? p.getProgress() : 0)
                .average()
                .orElse(0.0);
            stats.put("averageProgress", Math.round(avgProgress));

            teacherStats.put(teacherName, stats);
        }

        result.put("teacherStats", teacherStats);
        result.put("totalTeachers", teacherStats.size());

        return result;
    }

    /**
     * 获取项目质量分析
     */
    @Override
    public Map<String, Object> getProjectQualityAnalysis()
    {
        Map<String, Object> result = new HashMap<>();

        // 这里可以根据项目评估表来分析质量
        // 由于评估表可能还没有数据，先提供基础的质量指标

        List<PmsProject> projects = projectMapper.selectProjectList(new PmsProject());

        // 基于进度和时间计算质量指标
        long highQualityProjects = projects.stream()
            .filter(p -> p.getProgress() != null && p.getProgress() >= 80)
            .filter(p -> "ACTIVE".equals(p.getProjectStatus()) || "COMPLETED".equals(p.getProjectStatus()))
            .count();

        double qualityRate = projects.size() > 0 ? (double) highQualityProjects / projects.size() * 100 : 0;

        result.put("totalProjects", projects.size());
        result.put("highQualityProjects", highQualityProjects);
        result.put("qualityRate", Math.round(qualityRate));

        return result;
    }

    /**
     * 生成综合报表数据
     */
    @Override
    public Map<String, Object> generateComprehensiveReport(String reportType, String period)
    {
        Map<String, Object> result = new HashMap<>();

        // 综合各种分析数据
        result.put("projectOverview", getProjectOverview());
        result.put("workHourAnalysis", getWorkHourAnalysis(null));
        result.put("progressAnalysis", getProjectProgressAnalysis());
        result.put("materialCostAnalysis", getMaterialCostAnalysis(null));
        result.put("studentParticipation", getStudentParticipationAnalysis(null));
        result.put("teacherGuidance", getTeacherGuidanceAnalysis(null));
        result.put("qualityAnalysis", getProjectQualityAnalysis());

        result.put("reportType", reportType);
        result.put("period", period);
        result.put("generateTime", new Date());

        return result;
    }

    /**
     * 获取实时数据看板
     */
    @Override
    public Map<String, Object> getRealTimeDashboard()
    {
        Map<String, Object> result = new HashMap<>();

        // 实时统计数据
        List<PmsProject> allProjects = projectMapper.selectProjectList(new PmsProject());
        List<PmsTask> allTasks = taskMapper.selectTaskList(new PmsTask());

        // 今日新增项目
        Date today = new Date();
        long todayNewProjects = allProjects.stream()
            .filter(p -> p.getCreateTime() != null)
            .filter(p -> DateUtils.isSameDay(p.getCreateTime(), today))
            .count();

        // 今日完成任务
        long todayCompletedTasks = allTasks.stream()
            .filter(t -> "DONE".equals(t.getStatus()))
            .filter(t -> t.getUpdateTime() != null)
            .filter(t -> DateUtils.isSameDay(t.getUpdateTime(), today))
            .count();

        // 活跃项目数
        long activeProjects = allProjects.stream()
            .filter(p -> "ACTIVE".equals(p.getProjectStatus()))
            .count();

        // 待审批方案数
        // 这里需要查询方案表，暂时用0
        long pendingProposals = 0;

        result.put("totalProjects", allProjects.size());
        result.put("totalTasks", allTasks.size());
        result.put("todayNewProjects", todayNewProjects);
        result.put("todayCompletedTasks", todayCompletedTasks);
        result.put("activeProjects", activeProjects);
        result.put("pendingProposals", pendingProposals);
        result.put("updateTime", new Date());

        return result;
    }

    /**
     * 获取月度工时统计
     */
    private Map<String, BigDecimal> getMonthlyWorkHours(Long projectId)
    {
        Map<String, BigDecimal> monthlyHours = new HashMap<>();

        PmsWorkLog logQuery = new PmsWorkLog();
        if (projectId != null) {
            logQuery.setProjectId(projectId);
        }

        List<PmsWorkLog> workLogs = workLogMapper.selectWorkLogList(logQuery);
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");

        for (PmsWorkLog log : workLogs) {
            if (log.getLogDate() != null && log.getWorkHours() != null) {
                String month = monthFormat.format(log.getLogDate());
                monthlyHours.merge(month, log.getWorkHours(), BigDecimal::add);
            }
        }

        return monthlyHours;
    }
}
