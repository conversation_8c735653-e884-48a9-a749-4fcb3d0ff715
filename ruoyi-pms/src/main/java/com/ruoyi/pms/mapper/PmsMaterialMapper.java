package com.ruoyi.pms.mapper;

import java.util.List;
import com.ruoyi.pms.domain.PmsMaterial;
import org.apache.ibatis.annotations.Param;

/**
 * PMS材料清单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface PmsMaterialMapper
{
    /**
     * 查询材料清单
     * 
     * @param materialId 材料清单主键
     * @return 材料清单
     */
    public PmsMaterial selectMaterialByMaterialId(Long materialId);

    /**
     * 查询材料清单列表
     *
     * @param material 材料清单
     * @return 材料清单集合
     */
    public List<PmsMaterial> selectMaterialList(PmsMaterial material);

    /**
     * 根据项目ID查询材料列表
     *
     * @param projectId 项目ID
     * @return 材料集合
     */
    public List<PmsMaterial> selectMaterialsByProjectId(Long projectId);



    /**
     * 新增材料清单
     *
     * @param material 材料清单
     * @return 结果
     */
    public int insertMaterial(PmsMaterial material);

    /**
     * 修改材料清单
     *
     * @param material 材料清单
     * @return 结果
     */
    public int updateMaterial(PmsMaterial material);

    /**
     * 删除材料清单
     * 
     * @param materialId 材料清单主键
     * @return 结果
     */
    public int deleteMaterialByMaterialId(Long materialId);

    /**
     * 批量删除材料清单
     * 
     * @param materialIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialByMaterialIds(Long[] materialIds);

    /**
     * 根据项目ID删除所有材料
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteMaterialsByProjectId(Long projectId);

    /**
     * 批量新增材料
     *
     * @param materials 材料列表
     * @return 结果
     */
    public int batchInsertMaterials(List<PmsMaterial> materials);

    /**
     * 统计材料数量
     *
     * @param material 查询条件
     * @return 数量
     */
    public int countMaterials(PmsMaterial material);

    /**
     * 统计项目材料数量
     * 
     * @param projectId 项目ID
     * @return 数量
     */
    public int countMaterialsByProject(Long projectId);

    /**
     * 统计项目材料总价值
     *
     * @param projectId 项目ID
     * @return 总价值
     */
    public Double sumMaterialValueByProject(Long projectId);


}
