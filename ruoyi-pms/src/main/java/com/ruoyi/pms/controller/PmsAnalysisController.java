package com.ruoyi.pms.controller;

import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.pms.service.IPmsAnalysisService;

/**
 * PMS数据分析Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/pms/analysis")
public class PmsAnalysisController extends BaseController
{
    @Autowired
    private IPmsAnalysisService analysisService;

    /**
     * 获取项目统计概览
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/overview")
    public AjaxResult getProjectOverview()
    {
        Map<String, Object> data = analysisService.getProjectOverview();
        return success(data);
    }

    /**
     * 获取工时分析报告
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/workhour")
    public AjaxResult getWorkHourAnalysis(@RequestParam(value = "projectId", required = false) Long projectId)
    {
        Map<String, Object> data = analysisService.getWorkHourAnalysis(projectId);
        return success(data);
    }

    /**
     * 获取人员产值分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/productivity")
    public AjaxResult getPersonProductivityAnalysis(
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate)
    {
        Map<String, Object> data = analysisService.getPersonProductivityAnalysis(startDate, endDate);
        return success(data);
    }

    /**
     * 获取项目进度分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/progress")
    public AjaxResult getProjectProgressAnalysis()
    {
        Map<String, Object> data = analysisService.getProjectProgressAnalysis();
        return success(data);
    }


    /**
     * 获取学生项目参与度分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/student-participation")
    public AjaxResult getStudentParticipationAnalysis(@RequestParam(value = "classId", required = false) Long classId)
    {
        Map<String, Object> data = analysisService.getStudentParticipationAnalysis(classId);
        return success(data);
    }

    /**
     * 获取教师指导效果分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/teacher-guidance")
    public AjaxResult getTeacherGuidanceAnalysis(@RequestParam(value = "teacherId", required = false) Long teacherId)
    {
        Map<String, Object> data = analysisService.getTeacherGuidanceAnalysis(teacherId);
        return success(data);
    }

    /**
     * 获取项目质量分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/quality")
    public AjaxResult getProjectQualityAnalysis()
    {
        Map<String, Object> data = analysisService.getProjectQualityAnalysis();
        return success(data);
    }

    /**
     * 生成综合报表数据
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @Log(title = "生成综合报表", businessType = BusinessType.EXPORT)
    @GetMapping("/comprehensive-report")
    public AjaxResult generateComprehensiveReport(
            @RequestParam(value = "reportType", defaultValue = "monthly") String reportType,
            @RequestParam(value = "period", required = false) String period)
    {
        Map<String, Object> data = analysisService.generateComprehensiveReport(reportType, period);
        return success(data);
    }

    /**
     * 获取实时数据看板
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/dashboard")
    public AjaxResult getRealTimeDashboard()
    {
        Map<String, Object> data = analysisService.getRealTimeDashboard();
        return success(data);
    }

    /**
     * 获取项目详细分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/project/{projectId}")
    public AjaxResult getProjectDetailAnalysis(@PathVariable("projectId") Long projectId)
    {
        Map<String, Object> result = new java.util.HashMap<>();

        // 获取项目相关的各种分析数据
        result.put("workHourAnalysis", analysisService.getWorkHourAnalysis(projectId));
        result.put("materialCostAnalysis", analysisService.getMaterialCostAnalysis(projectId));

        return success(result);
    }

    /**
     * 获取教学评估数据
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/teaching-evaluation")
    public AjaxResult getTeachingEvaluationData(
            @RequestParam(value = "teacherId", required = false) Long teacherId,
            @RequestParam(value = "classId", required = false) Long classId,
            @RequestParam(value = "semester", required = false) String semester)
    {
        Map<String, Object> result = new java.util.HashMap<>();

        // 综合教师指导效果和学生参与度数据
        result.put("teacherGuidance", analysisService.getTeacherGuidanceAnalysis(teacherId));
        result.put("studentParticipation", analysisService.getStudentParticipationAnalysis(classId));
        result.put("qualityAnalysis", analysisService.getProjectQualityAnalysis());

        return success(result);
    }

    /**
     * 获取成本效益分析
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/cost-benefit")
    public AjaxResult getCostBenefitAnalysis(
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate)
    {
        Map<String, Object> result = new java.util.HashMap<>();

        // 综合项目概览、工时分析和材料成本分析
        result.put("projectOverview", analysisService.getProjectOverview());
        result.put("workHourAnalysis", analysisService.getWorkHourAnalysis(null));
        result.put("materialCostAnalysis", analysisService.getMaterialCostAnalysis(null));

        return success(result);
    }

    /**
     * 获取趋势分析数据
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:view')")
    @GetMapping("/trend")
    public AjaxResult getTrendAnalysis(
            @RequestParam(value = "period", defaultValue = "6") int period,
            @RequestParam(value = "unit", defaultValue = "month") String unit)
    {
        Map<String, Object> result = new java.util.HashMap<>();

        // 获取趋势数据（这里可以根据需要扩展具体的趋势分析逻辑）
        result.put("progressTrend", analysisService.getProjectProgressAnalysis());
        result.put("productivityTrend", analysisService.getPersonProductivityAnalysis(null, null));

        return success(result);
    }

    /**
     * 导出分析报告
     */
    @PreAuthorize("@ss.hasPermi('pms:analysis:export')")
    @Log(title = "导出分析报告", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void exportAnalysisReport(
            @RequestParam(value = "reportType", defaultValue = "comprehensive") String reportType,
            @RequestParam(value = "format", defaultValue = "excel") String format,
            HttpServletResponse response)
    {
        // 这里可以实现具体的导出逻辑
        // 暂时返回成功响应
        try {
            Map<String, Object> data = analysisService.generateComprehensiveReport(reportType, null);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=pms_analysis_report.xlsx");

            // 这里应该实现具体的Excel导出逻辑
            // 暂时写入简单的文本
            response.getWriter().write("PMS Analysis Report - " + new java.util.Date());

        } catch (Exception e) {
            logger.error("导出分析报告失败", e);
        }
    }
}
