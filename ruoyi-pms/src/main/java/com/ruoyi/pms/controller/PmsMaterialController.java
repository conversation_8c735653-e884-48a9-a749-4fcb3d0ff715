package com.ruoyi.pms.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.pms.domain.PmsMaterial;
import com.ruoyi.pms.service.IPmsMaterialService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * PMS材料清单Controller
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/pms/material")
public class PmsMaterialController extends BaseController
{
    @Autowired
    private IPmsMaterialService pmsMaterialService;

    /**
     * 查询材料清单列表
     */
    @PreAuthorize("@ss.hasPermi('pms:material:list')")
    @GetMapping("/list")
    public TableDataInfo list(PmsMaterial material)
    {
        startPage();
        List<PmsMaterial> list = pmsMaterialService.selectMaterialList(material);
        return getDataTable(list);
    }





    /**
     * 导出材料清单列表
     */
    @PreAuthorize("@ss.hasPermi('pms:material:export')")
    @Log(title = "材料清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PmsMaterial material)
    {
        List<PmsMaterial> list = pmsMaterialService.selectMaterialList(material);
        ExcelUtil<PmsMaterial> util = new ExcelUtil<PmsMaterial>(PmsMaterial.class);
        util.exportExcel(response, list, "材料清单数据");
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<PmsMaterial> util = new ExcelUtil<PmsMaterial>(PmsMaterial.class);
        util.importTemplateExcel(response, "材料数据");
    }



    /**
     * 获取材料清单详细信息
     */
    @PreAuthorize("@ss.hasPermi('pms:material:query')")
    @GetMapping(value = "/{materialId}")
    public AjaxResult getInfo(@PathVariable("materialId") Long materialId)
    {
        return success(pmsMaterialService.selectMaterialByMaterialId(materialId));
    }

    /**
     * 根据项目ID查询材料清单
     */
    @PreAuthorize("@ss.hasPermi('pms:material:query')")
    @GetMapping("/project/{projectId}")
    public TableDataInfo listByProject(@PathVariable("projectId") Long projectId)
    {
        startPage();
        PmsMaterial pmsMaterial = new PmsMaterial();
        pmsMaterial.setProjectId(projectId);
        List<PmsMaterial> list = pmsMaterialService.selectMaterialList(pmsMaterial);
        return getDataTable(list);
    }

    /**
     * 新增材料清单
     */
    @PreAuthorize("@ss.hasPermi('pms:material:add')")
    @Log(title = "材料清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PmsMaterial material)
    {
        return toAjax(pmsMaterialService.insertMaterial(material));
    }

    /**
     * 修改材料清单
     */
    @PreAuthorize("@ss.hasPermi('pms:material:edit')")
    @Log(title = "材料清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PmsMaterial material)
    {
        return toAjax(pmsMaterialService.updateMaterial(material));
    }

    /**
     * 删除材料清单
     */
    @PreAuthorize("@ss.hasPermi('pms:material:remove')")
    @Log(title = "材料清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{materialIds}")
    public AjaxResult remove(@PathVariable Long[] materialIds)
    {
        return toAjax(pmsMaterialService.deleteMaterialByMaterialIds(materialIds));
    }



    /**
     * 获取材料统计信息
     */
    @PreAuthorize("@ss.hasPermi('pms:material:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(Long projectId)
    {
        return success(pmsMaterialService.getMaterialStatistics(projectId));
    }

    /**
     * 生成材料采购清单
     */
    @PreAuthorize("@ss.hasPermi('pms:material:query')")
    @GetMapping("/purchase/{projectId}")
    public AjaxResult generatePurchaseList(@PathVariable("projectId") Long projectId)
    {
        return success(pmsMaterialService.generatePurchaseList(projectId));
    }

    /**
     * 导入材料数据
     */
    @PreAuthorize("@ss.hasPermi('pms:material:import')")
    @Log(title = "材料导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestParam("file") MultipartFile file, @RequestParam("projectId") Long projectId) throws Exception
    {
        ExcelUtil<PmsMaterial> util = new ExcelUtil<PmsMaterial>(PmsMaterial.class);
        List<PmsMaterial> materialList = util.importExcel(file.getInputStream());
        int result = pmsMaterialService.importMaterials(materialList, projectId);
        String message = "导入成功，共导入 " + result + " 条材料数据";
        return success(message);
    }

    /**
     * 上传资源包
     */
    @PreAuthorize("@ss.hasPermi('pms:material:edit')")
    @Log(title = "资源包上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload/{materialId}")
    public AjaxResult uploadResourceFile(@PathVariable("materialId") Long materialId, @RequestParam("file") MultipartFile file)
    {
        try {
            String resourceUrl = pmsMaterialService.uploadResourceFile(materialId, file);
            return success(resourceUrl);
        } catch (Exception e) {
            return error("资源包上传失败：" + e.getMessage());
        }
    }

    /**
     * 下载资源包
     */
    @PreAuthorize("@ss.hasPermi('pms:material:query')")
    @Log(title = "下载资源包", businessType = BusinessType.OTHER)
    @GetMapping("/download/{materialId}")
    public void downloadFile(@PathVariable("materialId") Long materialId, HttpServletResponse response)
    {
        try {
            PmsMaterial material = pmsMaterialService.selectMaterialByMaterialId(materialId);
            if (material == null || material.getResourceUrl() == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            String resourceUrl = material.getResourceUrl();
            String fileName = material.getMaterialName() + "_" + material.getSize();

            // 如果resourceUrl是相对路径，转换为绝对路径
            String filePath;
            if (resourceUrl.startsWith("/upload/")) {
                filePath = System.getProperty("user.dir") + resourceUrl;
            } else {
                filePath = resourceUrl;
            }

            File file = new File(filePath);
            if (!file.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" +
                URLEncoder.encode(fileName, "UTF-8") + "\"");
            response.setContentLength((int) file.length());

            // 输出文件内容
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
        } catch (Exception e) {
            logger.error("下载资源包失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 临时文件上传（用于新增材料时的文件上传）
     */
    // @PreAuthorize("@ss.hasPermi('pms:material:edit')")  // 临时注释权限检查
    @Log(title = "临时文件上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload/temp")
    public AjaxResult uploadTempFile(@RequestParam("file") MultipartFile file)
    {
        try {
            String fileUrl = pmsMaterialService.uploadTempFile(file);
            // 明确指定返回格式
            return AjaxResult.success("文件上传成功", fileUrl);
        } catch (Exception e) {
            return error("文件上传失败：" + e.getMessage());
        }
    }

    // ==================== 外部系统接口 ====================

    /**
     * 外部系统查询材料清单接口
     * 不需要权限验证，供其他系统调用
     */
    @GetMapping("/external/query")
    public AjaxResult externalQuery(@RequestParam(required = false) Long projectId,
                                   @RequestParam(required = false) String size,
                                   @RequestParam(required = false) String despite,
                                   @RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize)
    {
        try {
            PmsMaterial queryMaterial = new PmsMaterial();
            queryMaterial.setProjectId(projectId);
            queryMaterial.setSize(size);
            queryMaterial.setDespite(despite);

            startPage();
            List<PmsMaterial> list = pmsMaterialService.selectMaterialList(queryMaterial);

            return success(getDataTable(list));
        } catch (Exception e) {
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 外部系统上传材料清单接口
     * 不需要权限验证，供其他系统调用
     * 一次只能上传一个材料对象
     */
    @PostMapping("/external/upload")
    public AjaxResult externalUpload(@RequestBody PmsMaterial material)
    {
        try {
            if (material == null) {
                return error("材料数据不能为空");
            }

            // 验证必填字段
            if(StringUtils.isEmpty(material.getMaterialName())){
                return error("材料名称数据不能为空");
            }
            if(StringUtils.isEmpty(material.getResourceUrl())){
                return error("资源包URL不能为空");
            }

            // 插入材料数据
            int result = pmsMaterialService.insertMaterial(material);

            if (result > 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("materialId", material.getMaterialId());
                response.put("materialName", material.getMaterialName());
                response.put("resourceUrl", material.getResourceUrl());
                response.put("message", "材料上传成功");

                return success(response);
            } else {
                return error("材料上传失败");
            }
        } catch (Exception e) {
            return error("上传失败：" + e.getMessage());
        }
    }






}
