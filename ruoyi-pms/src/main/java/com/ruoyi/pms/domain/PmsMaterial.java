package com.ruoyi.pms.domain;

import java.math.BigDecimal;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * PMS材料清单对象 pms_material
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public class PmsMaterial extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 材料ID */
    private Long materialId;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 材料名称 */
    @Excel(name = "材料名称")
    private String materialName;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 资源包URL */
    private String resourceUrl;

    /** 描述 */
    @Excel(name = "描述")
    private String despite;

    /** 尺寸 */
    @Excel(name = "尺寸")
    private String size;

    /** 项目名称 */
    private String projectName;

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public String getDespite() {
        return despite;
    }

    public void setDespite(String despite) {
        this.despite = despite;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    @Override
    public String toString() {
        return "PmsMaterial{" +
                "materialId=" + materialId +
                ", projectId=" + projectId +
                ", materialName='" + materialName + '\'' +
                ", unitPrice=" + unitPrice +
                ", resourceUrl='" + resourceUrl + '\'' +
                ", despite='" + despite + '\'' +
                ", size='" + size + '\'' +
                ", projectName='" + projectName + '\'' +
                '}';
    }
}
