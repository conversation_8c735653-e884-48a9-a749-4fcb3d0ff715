package com.ruoyi.education.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.education.domain.*;
import com.ruoyi.education.domain.EduCourseChapter;
import com.ruoyi.education.domain.EduCourseLesson;
import com.ruoyi.education.service.*;
import com.ruoyi.education.mapper.LearningProgressMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;

/**
 * 学生端Controller
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/student")
public class StudentController extends BaseController
{
    @Autowired
    private ICourseService courseService;

    @Autowired
    private IStudentHomeworkService studentHomeworkService;

    @Autowired
    private IExamRecordService examRecordService; // 仍需要用于成绩查询

    @Autowired
    private IHomeworkService homeworkService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private LearningProgressMapper learningProgressMapper;

    @Autowired
    private IEduCourseContentService eduCourseContentService;

    @Autowired
    private IWrongQuestionService wrongQuestionService;

    /**
     * 判断课程是否分配给指定班级
     * @param deptIds 课程分配的班级ID字符串，多个用逗号分隔
     * @param targetDeptId 目标班级ID
     * @return 是否分配给该班级
     */
    private boolean isCourseBelongToDept(String deptIds, Long targetDeptId) {
        if (StringUtils.isEmpty(deptIds) || targetDeptId == null) {
            return false;
        }

        String[] deptIdArray = deptIds.split(",");
        for (String deptIdStr : deptIdArray) {
            try {
                Long deptId = Long.parseLong(deptIdStr.trim());
                if (deptId.equals(targetDeptId)) {
                    return true;
                }
            } catch (NumberFormatException e) {
                // 忽略无效的ID
                continue;
            }
        }
        return false;
    }

    /**
     * 检查课程是否可访问
     * @param course 课程对象
     * @return 是否可访问
     */
    private boolean isCourseAccessible(Course course) {
        if (course == null) {
            return false;
        }

        // 检查课程状态：只有已发布的课程才能访问
        if (!"0".equals(course.getStatus())) {
            return false;
        }

        // 可以添加更多检查条件，如：
        // - 课程开始时间
        // - 课程结束时间
        // - 课程容量限制等

        return true;
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public AjaxResult getUserProfile()
    {
        Map<String, Object> profile = new java.util.HashMap<>();

        // 基本信息
        profile.put("userId", SecurityUtils.getUserId());
        profile.put("username", SecurityUtils.getUsername());
        profile.put("nickname", SecurityUtils.getLoginUser().getUser().getNickName());
        profile.put("email", SecurityUtils.getLoginUser().getUser().getEmail());
        profile.put("phonenumber", SecurityUtils.getLoginUser().getUser().getPhonenumber());

        // 角色信息
        profile.put("roles", SecurityUtils.getLoginUser().getUser().getRoles());

        return success(profile);
    }

    /**
     * 获取学习仪表板数据
     */
    @GetMapping("/dashboard")
    public AjaxResult getDashboard()
    {
        Map<String, Object> dashboard = new HashMap<>();
        Long studentId = SecurityUtils.getUserId();

        try {
            // 1. 课程统计
            // TODO: 这里应该查询学生实际选修的课程，而不是所有课程
            Course courseQuery = new Course();
            courseQuery.setStatus("1"); // 启用的课程
            List<Course> allCourses = courseService.selectCourseList(courseQuery);
            Map<String, Object> courseStats = new HashMap<>();
            courseStats.put("total", allCourses.size());
            courseStats.put("inProgress", allCourses.stream().filter(c -> "1".equals(c.getStatus())).count());
            // TODO: 从学习进度表中获取实际完成的课程数量
            courseStats.put("completed", 0L); // 暂时设为0，等待实现学习进度功能
            dashboard.put("courseStats", courseStats);

            // 2. 作业统计
            List<StudentHomework> myHomework = studentHomeworkService.selectMyHomeworkList(null);
            Map<String, Object> homeworkStats = new HashMap<>();
            homeworkStats.put("total", myHomework.size());
            homeworkStats.put("pending", myHomework.stream().filter(h -> "0".equals(h.getStatus())).count());
            homeworkStats.put("submitted", myHomework.stream().filter(h -> "1".equals(h.getStatus())).count());
            homeworkStats.put("graded", myHomework.stream().filter(h -> "2".equals(h.getStatus())).count());
            dashboard.put("homeworkStats", homeworkStats);

            // 3. 考试统计
            ExamRecord recordQuery = new ExamRecord();
            recordQuery.setUserId(studentId);
            List<ExamRecord> examRecords = examRecordService.selectExamRecordList(recordQuery);
            Map<String, Object> examStats = new HashMap<>();
            examStats.put("total", examRecords.size());
            examStats.put("passed", examRecords.stream().filter(e -> e.getScore() != null && e.getScore() >= 60).count());
            dashboard.put("examStats", examStats);

            // 4. 最近学习活动
            List<Map<String, Object>> recentActivities = new ArrayList<>();

            // 添加最近的作业活动
            myHomework.stream()
                    .limit(3)
                    .forEach(homework -> {
                        Map<String, Object> activity = new HashMap<>();
                        activity.put("type", "homework");
                        activity.put("title", "作业 #" + homework.getHomeworkId());
                        activity.put("time", homework.getUpdateTime());
                        activity.put("status", homework.getStatus());
                        recentActivities.add(activity);
                    });

            dashboard.put("recentActivities", recentActivities);

            return success(dashboard);

        } catch (Exception e) {
            logger.error("获取学生仪表板数据失败", e);
            // 返回空的默认数据，避免显示误导性的静态数据
            Map<String, Object> defaultCourseStats = new HashMap<>();
            defaultCourseStats.put("total", 0);
            defaultCourseStats.put("inProgress", 0);
            defaultCourseStats.put("completed", 0);
            dashboard.put("courseStats", defaultCourseStats);

            Map<String, Object> defaultHomeworkStats = new HashMap<>();
            defaultHomeworkStats.put("total", 0);
            defaultHomeworkStats.put("pending", 0);
            defaultHomeworkStats.put("submitted", 0);
            defaultHomeworkStats.put("graded", 0);
            dashboard.put("homeworkStats", defaultHomeworkStats);

            Map<String, Object> defaultExamStats = new HashMap<>();
            defaultExamStats.put("total", 0);
            defaultExamStats.put("passed", 0);
            dashboard.put("examStats", defaultExamStats);
            dashboard.put("recentActivities", new ArrayList<>());
            return success(dashboard);
        }
    }

    /**
     * 获取我的课程列表
     */
    @GetMapping("/courses")
    public AjaxResult getMyCourses()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 获取当前学生的部门ID（班级ID）
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            Long studentDeptId = currentUser.getDeptId();

            // 根据学生班级查询相关课程
            Course courseQuery = new Course();
            courseQuery.setStatus("0"); // 只返回已发布的课程
            List<Course> allCourses = courseService.selectCourseList(courseQuery);

            // 过滤出分配给该班级的课程，并验证权限
            List<Course> courseList = new ArrayList<>();
            for (Course course : allCourses) {
                if (isCourseBelongToDept(course.getDeptIds(), studentDeptId) &&
                        isCourseAccessible(course)) {
                    courseList.add(course);
                }
            }

            // 为每个课程添加学习进度信息
            List<Map<String, Object>> result = courseList.stream().map(c -> {
                Map<String, Object> courseInfo = new HashMap<>();
                courseInfo.put("courseId", c.getCourseId());
                courseInfo.put("courseName", c.getCourseName());
                courseInfo.put("description", c.getDescription());
                courseInfo.put("coverImage", c.getCoverImage());
                courseInfo.put("courseCode", c.getCourseCode());
                courseInfo.put("totalHours", c.getTotalHours());
                courseInfo.put("credits", c.getCredits());
                courseInfo.put("status", c.getStatus());
                courseInfo.put("createTime", c.getCreateTime());
                courseInfo.put("updateTime", c.getUpdateTime());

                // 从学习进度表中获取真实的进度数据
                try {
                    // 使用LearningProgressMapper计算学习进度
                    Integer progress = learningProgressMapper.calculateCourseProgress(studentId, c.getCourseId());
                    courseInfo.put("progress", progress != null ? progress : 0);

                    Integer completedLessons = learningProgressMapper.getCompletedLessonsCount(studentId, c.getCourseId());
                    Integer totalLessons = learningProgressMapper.getTotalLessonsCount(c.getCourseId());

                    courseInfo.put("completedLessons", completedLessons != null ? completedLessons : 0);
                    courseInfo.put("totalLessons", totalLessons != null ? totalLessons : 0);
                } catch (Exception e) {
                    logger.warn("获取课程{}学习进度失败: {}", c.getCourseId(), e.getMessage());
                    courseInfo.put("progress", 0);
                    courseInfo.put("completedLessons", 0);
                    courseInfo.put("totalLessons", 0);
                }

                // TODO: 从教师表中获取教师信息
                courseInfo.put("teacherName", "待分配"); // 默认教师名称

                return courseInfo;
            }).collect(Collectors.toList());

            return success(result);

        } catch (Exception e) {
            logger.error("获取我的课程列表失败", e);
            return error("获取课程列表失败");
        }
    }

    /**
     * 获取我的作业列表
     */
    @GetMapping("/homework")
    public AjaxResult getMyHomework(@RequestParam(required = false) String status)
    {
        try {
            List<StudentHomework> homeworkList = studentHomeworkService.selectMyHomeworkList(status);
            return success(homeworkList);
        } catch (Exception e) {
            logger.error("获取我的作业列表失败", e);
            return error("获取作业列表失败");
        }
    }

    /**
     * 获取我的成绩列表
     */
    @GetMapping("/grades")
    public AjaxResult getMyGrades()
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            ExamRecord recordQuery = new ExamRecord();
            recordQuery.setUserId(studentId);
            List<ExamRecord> records = examRecordService.selectExamRecordList(recordQuery);
            return success(records);
        } catch (Exception e) {
            logger.error("获取我的成绩列表失败", e);
            return error("获取成绩列表失败");
        }
    }

    // ==================== 作业详细接口 ====================

    /**
     * 获取作业详情
     */
    @GetMapping("/homework/{homeworkId}")
    public AjaxResult getHomeworkDetail(@PathVariable Long homeworkId)
    {
        try {
            StudentHomework homework = studentHomeworkService.selectHomeworkDetail(homeworkId);
            if (homework == null) {
                return error("作业不存在或无权限访问");
            }
            return success(homework);
        } catch (Exception e) {
            logger.error("获取作业详情失败", e);
            return error("获取作业详情失败");
        }
    }

    /**
     * 获取作业题目列表
     */
    @GetMapping("/homework/{homeworkId}/questions")
    public AjaxResult getHomeworkQuestions(@PathVariable Long homeworkId)
    {
        try {
            // 先验证学生是否有权限访问该作业
            StudentHomework homework = studentHomeworkService.selectHomeworkDetail(homeworkId);
            if (homework == null) {
                return error("作业不存在或无权限访问");
            }

            // 获取作业题目列表
            List<Map<String, Object>> questions = studentHomeworkService.getHomeworkQuestions(homeworkId);
            return success(questions);
        } catch (Exception e) {
            logger.error("获取作业题目失败", e);
            return error("获取作业题目失败");
        }
    }

    // ==================== 错题本接口 ====================

    /**
     * 获取我的错题本
     */
    @GetMapping("/wrong-questions")
    public AjaxResult getMyWrongQuestions(@RequestParam(required = false) String questionContent,
                                         @RequestParam(required = false) String subject,
                                         @RequestParam(required = false) String questionType,
                                         @RequestParam(required = false) String difficultyLevel)
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("questionContent", questionContent);
            queryParams.put("subject", subject);
            queryParams.put("questionType", questionType);
            queryParams.put("difficultyLevel", difficultyLevel);

            List<WrongQuestion> wrongQuestions = wrongQuestionService.selectStudentWrongQuestions(studentId, queryParams);
            return success(wrongQuestions);
        } catch (Exception e) {
            logger.error("获取错题本失败", e);
            return error("获取错题本失败：" + e.getMessage());
        }
    }

    /**
     * 标记错题为已掌握
     */
    @PutMapping("/wrong-questions/{id}/master")
    public AjaxResult markWrongQuestionMastered(@PathVariable Long id)
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            int result = wrongQuestionService.markAsMastered(id, studentId);
            if (result > 0) {
                return success("标记成功");
            } else {
                return error("标记失败");
            }
        } catch (Exception e) {
            logger.error("标记错题掌握状态失败", e);
            return error("标记失败：" + e.getMessage());
        }
    }

    /**
     * 更新错题笔记
     */
    @PutMapping("/wrong-questions/{id}/notes")
    public AjaxResult updateWrongQuestionNotes(@PathVariable Long id, @RequestBody Map<String, Object> data)
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            String notes = (String) data.get("notes");
            int result = wrongQuestionService.updateNotes(id, studentId, notes);
            if (result > 0) {
                return success("笔记更新成功");
            } else {
                return error("笔记更新失败");
            }
        } catch (Exception e) {
            logger.error("更新错题笔记失败", e);
            return error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除错题
     */
    @DeleteMapping("/wrong-questions/{id}")
    public AjaxResult deleteWrongQuestion(@PathVariable Long id)
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            // 验证权限
            WrongQuestion wrongQuestion = wrongQuestionService.selectWrongQuestionById(id);
            if (wrongQuestion == null) {
                return error("错题记录不存在");
            }
            if (!wrongQuestion.getStudentId().equals(studentId)) {
                return error("无权限删除此错题记录");
            }

            int result = wrongQuestionService.deleteWrongQuestionById(id);
            if (result > 0) {
                return success("删除成功");
            } else {
                return error("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除错题失败", e);
            return error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取错题统计信息
     */
    @GetMapping("/wrong-questions/statistics")
    public AjaxResult getWrongQuestionStatistics()
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            Map<String, Object> statistics = wrongQuestionService.getWrongQuestionStatistics(studentId);
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取错题统计失败", e);
            return error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 提交作业
     */
    @PostMapping("/homework/{homeworkId}/submit")
    public AjaxResult submitHomework(@PathVariable Long homeworkId,
                                     @RequestParam String answerContent,
                                     @RequestParam(required = false) MultipartFile attachmentFile)
    {
        try {
            // 检查作业是否已提交
            if (studentHomeworkService.isHomeworkSubmitted(homeworkId)) {
                return error("作业已提交，不能重复提交");
            }

            int result = studentHomeworkService.submitHomework(homeworkId, answerContent, attachmentFile);
            if (result > 0) {
                return success("作业提交成功");
            } else {
                return error("作业提交失败");
            }
        } catch (Exception e) {
            logger.error("提交作业失败", e);
            return error("提交作业失败：" + e.getMessage());
        }
    }

    /**
     * 保存作业草稿
     */
    @PostMapping("/homework/{homeworkId}/draft")
    public AjaxResult saveDraft(@PathVariable Long homeworkId,
                                @RequestParam String answerContent,
                                @RequestParam(required = false) MultipartFile attachmentFile)
    {
        try {
            int result = studentHomeworkService.saveDraft(homeworkId, answerContent, attachmentFile);
            if (result > 0) {
                return success("草稿保存成功");
            } else {
                return error("草稿保存失败");
            }
        } catch (Exception e) {
            logger.error("保存作业草稿失败", e);
            return error("保存草稿失败：" + e.getMessage());
        }
    }

    /**
     * 撤回作业提交
     */
    @PutMapping("/homework/{homeworkId}/withdraw")
    public AjaxResult withdrawSubmission(@PathVariable Long homeworkId)
    {
        try {
            int result = studentHomeworkService.withdrawSubmission(homeworkId);
            if (result > 0) {
                return success("作业撤回成功");
            } else {
                return error("作业撤回失败");
            }
        } catch (Exception e) {
            logger.error("撤回作业提交失败", e);
            return error("撤回作业失败：" + e.getMessage());
        }
    }

    /**
     * 获取待办作业
     */
    @GetMapping("/homework/pending")
    public AjaxResult getPendingHomework()
    {
        try {
            List<StudentHomework> homeworkList = studentHomeworkService.getPendingHomeworkList();
            return success(homeworkList);
        } catch (Exception e) {
            logger.error("获取待办作业失败", e);
            return error("获取待办作业失败");
        }
    }

    /**
     * 获取已完成作业
     */
    @GetMapping("/homework/completed")
    public AjaxResult getCompletedHomework()
    {
        try {
            List<StudentHomework> homeworkList = studentHomeworkService.getCompletedHomeworkList();
            return success(homeworkList);
        } catch (Exception e) {
            logger.error("获取已完成作业失败", e);
            return error("获取已完成作业失败");
        }
    }

    /**
     * 获取已批改作业
     */
    @GetMapping("/homework/graded")
    public AjaxResult getGradedHomework()
    {
        try {
            List<StudentHomework> homeworkList = studentHomeworkService.getGradedHomeworkList();
            return success(homeworkList);
        } catch (Exception e) {
            logger.error("获取已批改作业失败", e);
            return error("获取已批改作业失败");
        }
    }

    /**
     * 下载作业附件
     */
    @PostMapping("/homework/{studentHomeworkId}/download")
    public AjaxResult downloadAttachment(@PathVariable Long studentHomeworkId)
    {
        try {
            String filePath = studentHomeworkService.downloadAttachment(studentHomeworkId);
            if (StringUtils.isNotEmpty(filePath)) {
                Map<String, Object> result = new HashMap<>();
                result.put("filePath", filePath);
                return success(result);
            } else {
                return error("文件不存在");
            }
        } catch (Exception e) {
            logger.error("下载作业附件失败", e);
            return error("下载附件失败");
        }
    }

    /**
     * 获取作业统计信息
     */
    @GetMapping("/homework/statistics")
    public AjaxResult getHomeworkStatistics()
    {
        try {
            Map<String, Object> statistics = studentHomeworkService.getHomeworkStatistics();
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取作业统计信息失败", e);
            return error("获取统计信息失败");
        }
    }

    // ==================== 课程详细接口 ====================

    /**
     * 获取课程详情
     */
    @GetMapping("/courses/{courseId}/detail")
    public AjaxResult getCourseDetail(@PathVariable Long courseId)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 验证学生是否有权限访问该课程
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            Course course = courseService.selectCourseByCourseId(courseId);
            if (course == null) {
                return error("课程不存在");
            }

            // 检查该课程是否分配给学生所在班级
            if (!isCourseBelongToDept(course.getDeptIds(), currentUser.getDeptId())) {
                return error("您没有权限访问该课程");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("courseId", course.getCourseId());
            result.put("courseName", course.getCourseName());
            result.put("description", course.getDescription());
            result.put("coverImage", course.getCoverImage());
            result.put("totalHours", course.getTotalHours());
            result.put("credits", course.getCredits());
            result.put("status", course.getStatus());
            result.put("createTime", course.getCreateTime());

            // 模拟学习进度数据
            result.put("progress", Math.random() * 100);
            result.put("totalStudyTime", (int)(Math.random() * 3600));
            result.put("lastStudyTime", new Date());

            return success(result);
        } catch (Exception e) {
            logger.error("获取课程详情失败", e);
            return error("获取课程详情失败");
        }
    }

    /**
     * 获取课程内容（章节和课时）
     */
    @GetMapping("/courses/{courseId}/content")
    public AjaxResult getCourseContent(@PathVariable Long courseId)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 验证学生是否有权限访问该课程
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            Course course = courseService.selectCourseByCourseId(courseId);
            if (course == null) {
                return error("课程不存在");
            }

            // 检查该课程是否分配给学生所在班级
            if (!isCourseBelongToDept(course.getDeptIds(), currentUser.getDeptId())) {
                return error("您没有权限访问该课程");
            }

            // 获取真实的课程内容（章节和课时）
            List<EduCourseChapter> chapters = eduCourseContentService.getCourseContent(courseId);

            // 转换为前端需要的格式，并添加学习进度信息
            List<Map<String, Object>> result = new ArrayList<>();
            for (EduCourseChapter chapter : chapters) {
                Map<String, Object> chapterData = new HashMap<>();
                chapterData.put("id", chapter.getChapterId());
                chapterData.put("name", chapter.getChapterName());
                chapterData.put("description", chapter.getChapterDescription());
                chapterData.put("sortOrder", chapter.getSortOrder());

                // 处理课时列表
                List<Map<String, Object>> lessonList = new ArrayList<>();
                if (chapter.getLessons() != null) {
                    for (EduCourseLesson lesson : chapter.getLessons()) {
                        Map<String, Object> lessonData = new HashMap<>();
                        lessonData.put("id", lesson.getLessonId());
                        lessonData.put("name", lesson.getLessonName());
                        lessonData.put("type", lesson.getLessonType());
                        lessonData.put("duration", lesson.getDuration());
                        lessonData.put("description", lesson.getLessonDescription());
                        lessonData.put("videoUrl", lesson.getVideoUrl());
                        lessonData.put("audioUrl", lesson.getAudioUrl());
                        lessonData.put("sortOrder", lesson.getSortOrder());

                        // 检查学生是否已完成该课时
                        try {
                            Integer completed = learningProgressMapper.checkLessonCompleted(studentId, lesson.getLessonId());
                            lessonData.put("completed", completed != null && completed == 1);
                        } catch (Exception e) {
                            logger.warn("检查课时{}完成状态失败: {}", lesson.getLessonId(), e.getMessage());
                            lessonData.put("completed", false);
                        }

                        lessonList.add(lessonData);
                    }
                }

                chapterData.put("children", lessonList);
                result.add(chapterData);
            }

            return success(result);
        } catch (Exception e) {
            logger.error("获取课程内容失败", e);
            return error("获取课程内容失败");
        }
    }

    /**
     * 选课
     */
    @PostMapping("/courses/{courseId}/enroll")
    public AjaxResult enrollCourse(@PathVariable Long courseId)
    {
        try {
            // 这里应该实现选课逻辑，暂时返回成功
            // 实际应该检查课程是否存在、是否已选、是否有冲突等
            Course course = courseService.selectCourseByCourseId(courseId);
            if (course == null) {
                return error("课程不存在");
            }

            // TODO: 实现选课逻辑
            return success("选课成功");
        } catch (Exception e) {
            logger.error("选课失败", e);
            return error("选课失败：" + e.getMessage());
        }
    }

    /**
     * 退课
     */
    @DeleteMapping("/courses/{courseId}/drop")
    public AjaxResult dropCourse(@PathVariable Long courseId)
    {
        try {
            // 这里应该实现退课逻辑，暂时返回成功
            // 实际应该检查课程是否存在、是否已选、是否可以退课等
            Course course = courseService.selectCourseByCourseId(courseId);
            if (course == null) {
                return error("课程不存在");
            }

            // TODO: 实现退课逻辑
            return success("退课成功");
        } catch (Exception e) {
            logger.error("退课失败", e);
            return error("退课失败：" + e.getMessage());
        }
    }

    /**
     * 更新学习进度
     */
    @PutMapping("/courses/{courseId}/progress")
    public AjaxResult updateProgress(@PathVariable Long courseId, @RequestBody Map<String, Object> data)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 验证课程是否存在且学生有权限访问
            Course course = courseService.selectCourseByCourseId(courseId);
            if (course == null) {
                return error("课程不存在");
            }

            SysUser currentUser = userService.selectUserById(studentId);
            if (!isCourseBelongToDept(course.getDeptIds(), currentUser.getDeptId())) {
                return error("您没有权限访问该课程");
            }

            // 支持批量更新已完成的课时
            Object completedLessonIdsObj = data.get("completedLessonIds");
            if (completedLessonIdsObj != null && completedLessonIdsObj instanceof List) {
                List<?> lessonIdList = (List<?>) completedLessonIdsObj;
                int successCount = 0;

                for (Object lessonIdObj : lessonIdList) {
                    try {
                        Long lessonId = Long.valueOf(lessonIdObj.toString());
                        int result = learningProgressMapper.recordLessonProgress(studentId, lessonId, courseId);
                        if (result > 0) {
                            successCount++;
                        }
                    } catch (Exception e) {
                        logger.warn("更新课时{}学习进度失败: {}", lessonIdObj, e.getMessage());
                    }
                }

                logger.info("批量更新学习进度完成，成功更新{}个课时", successCount);
                return success("学习进度更新成功，已更新" + successCount + "个课时");
            }

            // 兼容单个课时更新
            Object lessonIdObj = data.get("lessonId");
            if (lessonIdObj != null) {
                Long lessonId = Long.valueOf(lessonIdObj.toString());
                int result = learningProgressMapper.recordLessonProgress(studentId, lessonId, courseId);

                if (result > 0) {
                    return success("学习进度更新成功");
                } else {
                    return error("学习进度更新失败");
                }
            }

            return error("请提供课时ID或已完成课时列表");
        } catch (Exception e) {
            logger.error("更新学习进度失败", e);
            return error("更新学习进度失败：" + e.getMessage());
        }
    }

    /**
     * 获取学生成绩分析数据
     */
    @GetMapping("/score-analysis")
    public AjaxResult getScoreAnalysis()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 获取学生的考试记录
            ExamRecord queryRecord = new ExamRecord();
            queryRecord.setUserId(studentId);
            List<ExamRecord> examRecords = examRecordService.selectExamRecordList(queryRecord);

            Map<String, Object> analysisData = new HashMap<>();

            if (examRecords.isEmpty()) {
                // 返回空数据
                analysisData.put("scoreStats", getEmptyScoreStats());
                analysisData.put("subjectRanking", new ArrayList<>());
                analysisData.put("examRecords", new ArrayList<>());
                analysisData.put("scoreDistribution", getEmptyScoreDistribution());
                return success(analysisData);
            }

            // 计算成绩统计
            analysisData.put("scoreStats", calculateScoreStats(examRecords));

            // 计算科目排名
            analysisData.put("subjectRanking", calculateSubjectRanking(examRecords));

            // 获取最近考试记录
            analysisData.put("examRecords", getRecentExamRecords(examRecords));

            // 计算成绩分布
            analysisData.put("scoreDistribution", calculateScoreDistribution(examRecords));

            return success(analysisData);
        } catch (Exception e) {
            logger.error("获取成绩分析数据失败", e);
            return error("获取成绩分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 计算成绩统计
     */
    private List<Map<String, Object>> calculateScoreStats(List<ExamRecord> examRecords) {
        List<Map<String, Object>> stats = new ArrayList<>();

        if (examRecords.isEmpty()) {
            return getEmptyScoreStats();
        }

        // 计算平均分
        double averageScore = examRecords.stream()
            .filter(record -> record.getScore() != null)
            .mapToInt(ExamRecord::getScore)
            .average()
            .orElse(0.0);

        // 计算最高分
        int highestScore = examRecords.stream()
            .filter(record -> record.getScore() != null)
            .mapToInt(ExamRecord::getScore)
            .max()
            .orElse(0);

        // 计算最低分
        int lowestScore = examRecords.stream()
            .filter(record -> record.getScore() != null)
            .mapToInt(ExamRecord::getScore)
            .min()
            .orElse(0);

        // 考试次数
        int examCount = examRecords.size();

        stats.add(createStatItem("average", "平均分", String.format("%.1f", averageScore), "el-icon-trophy", "#409EFF"));
        stats.add(createStatItem("highest", "最高分", String.valueOf(highestScore), "el-icon-star-on", "#67C23A"));
        stats.add(createStatItem("lowest", "最低分", String.valueOf(lowestScore), "el-icon-warning", "#F56C6C"));
        stats.add(createStatItem("exams", "考试次数", String.valueOf(examCount), "el-icon-document-checked", "#E6A23C"));

        return stats;
    }

    /**
     * 创建统计项
     */
    private Map<String, Object> createStatItem(String key, String label, String value, String icon, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("key", key);
        item.put("label", label);
        item.put("value", value);
        item.put("icon", icon);
        item.put("color", color);
        return item;
    }

    /**
     * 获取空的成绩统计
     */
    private List<Map<String, Object>> getEmptyScoreStats() {
        List<Map<String, Object>> stats = new ArrayList<>();
        stats.add(createStatItem("average", "平均分", "0", "el-icon-trophy", "#409EFF"));
        stats.add(createStatItem("highest", "最高分", "0", "el-icon-star-on", "#67C23A"));
        stats.add(createStatItem("lowest", "最低分", "0", "el-icon-warning", "#F56C6C"));
        stats.add(createStatItem("exams", "考试次数", "0", "el-icon-document-checked", "#E6A23C"));
        return stats;
    }

    /**
     * 计算科目排名
     */
    private List<Map<String, Object>> calculateSubjectRanking(List<ExamRecord> examRecords) {
        // 按科目分组计算平均分
        Map<String, List<Integer>> subjectScores = new HashMap<>();

        for (ExamRecord record : examRecords) {
            if (record.getScore() != null) {
                String subject = getSubjectFromExamName(record.getExamName());
                subjectScores.computeIfAbsent(subject, k -> new ArrayList<>()).add(record.getScore());
            }
        }

        List<Map<String, Object>> ranking = new ArrayList<>();
        for (Map.Entry<String, List<Integer>> entry : subjectScores.entrySet()) {
            String subject = entry.getKey();
            List<Integer> scores = entry.getValue();
            double avgScore = scores.stream().mapToInt(Integer::intValue).average().orElse(0.0);

            Map<String, Object> subjectData = new HashMap<>();
            subjectData.put("name", subject);
            subjectData.put("score", Math.round(avgScore));
            ranking.add(subjectData);
        }

        // 按分数降序排序
        ranking.sort((a, b) -> Integer.compare((Integer) b.get("score"), (Integer) a.get("score")));

        return ranking;
    }

    /**
     * 从考试名称中提取科目
     */
    private String getSubjectFromExamName(String examName) {
        if (examName == null) return "其他";

        if (examName.contains("数学")) return "数学";
        if (examName.contains("英语")) return "英语";
        if (examName.contains("语文")) return "语文";
        if (examName.contains("物理")) return "物理";
        if (examName.contains("化学")) return "化学";
        if (examName.contains("生物")) return "生物";
        if (examName.contains("历史")) return "历史";
        if (examName.contains("地理")) return "地理";
        if (examName.contains("政治")) return "政治";

        return "其他";
    }

    /**
     * 获取最近考试记录
     */
    private List<Map<String, Object>> getRecentExamRecords(List<ExamRecord> examRecords) {
        return examRecords.stream()
            .filter(record -> record.getScore() != null)
            .sorted((a, b) -> {
                Date dateA = a.getSubmitTime() != null ? a.getSubmitTime() : a.getCreateTime();
                Date dateB = b.getSubmitTime() != null ? b.getSubmitTime() : b.getCreateTime();
                return dateB.compareTo(dateA); // 降序排序，最新的在前
            })
            .limit(10) // 只取最近10次
            .map(record -> {
                Map<String, Object> examData = new HashMap<>();
                examData.put("examName", record.getExamName());
                examData.put("subject", getSubjectFromExamName(record.getExamName()));
                examData.put("score", record.getScore());
                Date examDate = record.getSubmitTime() != null ? record.getSubmitTime() : record.getCreateTime();
                examData.put("examDate", com.ruoyi.common.utils.DateUtils.dateTime(examDate));
                return examData;
            })
            .collect(Collectors.toList());
    }

    /**
     * 计算成绩分布
     */
    private List<Map<String, Object>> calculateScoreDistribution(List<ExamRecord> examRecords) {
        List<Map<String, Object>> distribution = new ArrayList<>();

        if (examRecords.isEmpty()) {
            return getEmptyScoreDistribution();
        }

        // 统计各分数段的数量
        int count90_100 = 0, count80_89 = 0, count70_79 = 0, count60_69 = 0, count0_59 = 0;

        for (ExamRecord record : examRecords) {
            if (record.getScore() != null) {
                int score = record.getScore();
                if (score >= 90) count90_100++;
                else if (score >= 80) count80_89++;
                else if (score >= 70) count70_79++;
                else if (score >= 60) count60_69++;
                else count0_59++;
            }
        }

        int total = examRecords.size();

        distribution.add(createDistributionItem("90-100分", count90_100, total, "#67C23A"));
        distribution.add(createDistributionItem("80-89分", count80_89, total, "#409EFF"));
        distribution.add(createDistributionItem("70-79分", count70_79, total, "#E6A23C"));
        distribution.add(createDistributionItem("60-69分", count60_69, total, "#F56C6C"));
        distribution.add(createDistributionItem("0-59分", count0_59, total, "#909399"));

        return distribution;
    }

    /**
     * 创建分布项
     */
    private Map<String, Object> createDistributionItem(String label, int count, int total, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("label", label);
        item.put("count", count);
        item.put("percentage", total > 0 ? Math.round((count * 100.0) / total) : 0);
        item.put("color", color);
        return item;
    }

    /**
     * 获取空的成绩分布
     */
    private List<Map<String, Object>> getEmptyScoreDistribution() {
        List<Map<String, Object>> distribution = new ArrayList<>();
        distribution.add(createDistributionItem("90-100分", 0, 0, "#67C23A"));
        distribution.add(createDistributionItem("80-89分", 0, 0, "#409EFF"));
        distribution.add(createDistributionItem("70-79分", 0, 0, "#E6A23C"));
        distribution.add(createDistributionItem("60-69分", 0, 0, "#F56C6C"));
        distribution.add(createDistributionItem("0-59分", 0, 0, "#909399"));
        return distribution;
    }

    /**
     * 获取推荐课程
     */
    @GetMapping("/courses/recommended")
    public AjaxResult getRecommendedCourses()
    {
        try {
            // 这里应该实现推荐算法，暂时返回所有启用的课程
            Course course = new Course();
            course.setStatus("1");
            List<Course> courseList = courseService.selectCourseList(course);

            // 随机选择几个作为推荐课程
            List<Course> recommendedCourses = courseList.stream()
                    .limit(6)
                    .collect(Collectors.toList());

            return success(recommendedCourses);
        } catch (Exception e) {
            logger.error("获取推荐课程失败", e);
            return error("获取推荐课程失败");
        }
    }

    /**
     * 获取课程统计信息
     */
    @GetMapping("/courses/statistics")
    public AjaxResult getCourseStatistics()
    {
        try {
            Long studentId = SecurityUtils.getUserId();
            Map<String, Object> statistics = new HashMap<>();

            // 获取当前学生的部门ID（班级ID）
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            Long studentDeptId = currentUser.getDeptId();

            // 根据学生班级查询相关课程
            Course courseQuery = new Course();
            courseQuery.setStatus("0"); // 只返回已发布的课程
            List<Course> allCourses = courseService.selectCourseList(courseQuery);

            // 过滤出分配给该班级的课程
            List<Course> courseList = new ArrayList<>();
            for (Course course : allCourses) {
                if (isCourseBelongToDept(course.getDeptIds(), studentDeptId) &&
                        isCourseAccessible(course)) {
                    courseList.add(course);
                }
            }

            int totalCourses = courseList.size();
            // 模拟学习进度统计（实际应该从学习进度表查询）
            int inProgressCourses = (int)(totalCourses * 0.6);
            int completedCourses = (int)(totalCourses * 0.3);
            int notStartedCourses = totalCourses - inProgressCourses - completedCourses;

            statistics.put("totalCourses", totalCourses);
            statistics.put("inProgressCourses", inProgressCourses);
            statistics.put("completedCourses", completedCourses);
            statistics.put("notStartedCourses", notStartedCourses);
            statistics.put("totalStudyHours", totalCourses * 20); // 模拟数据
            statistics.put("averageProgress", totalCourses > 0 ? 45.0 : 0.0); // 模拟数据

            return success(statistics);
        } catch (Exception e) {
            logger.error("获取课程统计信息失败", e);
            return error("获取统计信息失败");
        }
    }

    // ==================== 考试相关接口 ====================

    @Autowired
    private IExamScheduleService examScheduleService;

    @Autowired
    private IPaperTemplateService paperTemplateService;

    /**
     * 获取我的考试列表
     */
    @GetMapping("/exams")
    public AjaxResult getMyExams(@RequestParam(value = "status", required = false) String status)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 获取学生部门信息
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            // 查询考试安排 - 只查询分配给学生班级的考试
            ExamSchedule scheduleQuery = new ExamSchedule();
            if (status != null) {
                scheduleQuery.setStatus(status);
            }
            List<ExamSchedule> allSchedules = examScheduleService.selectExamScheduleList(scheduleQuery);

            // 过滤出学生可参加的考试（检查deptIds字段）
            List<ExamSchedule> examSchedules = new ArrayList<>();
            for (ExamSchedule schedule : allSchedules) {
                if (schedule.getDeptIds() != null && !schedule.getDeptIds().isEmpty()) {
                    String[] deptIdArray = schedule.getDeptIds().split(",");
                    for (String deptIdStr : deptIdArray) {
                        try {
                            Long deptId = Long.valueOf(deptIdStr.trim());
                            if (deptId.equals(currentUser.getDeptId())) {
                                examSchedules.add(schedule);
                                break;
                            }
                        } catch (NumberFormatException e) {
                            // 忽略无效的部门ID
                        }
                    }
                }
            }

            // 转换状态为前端期望的格式，并检查学生的考试记录
            Date now = new Date();
            for (ExamSchedule schedule : examSchedules) {
                String frontendStatus = convertStatusForFrontend(schedule, now, studentId);
                schedule.setStatus(frontendStatus);
            }

            return success(examSchedules);
        } catch (Exception e) {
            logger.error("获取考试列表失败", e);
            return error("获取考试列表失败");
        }
    }

    /**
     * 开始考试
     */
    @PostMapping("/exams/{examId}/start")
    public AjaxResult startExam(@PathVariable("examId") Long examId)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 获取考试安排信息
            ExamSchedule examSchedule = examScheduleService.selectExamScheduleByExamId(examId);
            if (examSchedule == null) {
                return error("考试不存在");
            }

            // 验证学生是否有权限参加该考试
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            // 检查考试是否分配给学生所在班级
            boolean canParticipate = false;
            if (examSchedule.getDeptIds() != null && !examSchedule.getDeptIds().isEmpty()) {
                String[] deptIdArray = examSchedule.getDeptIds().split(",");
                for (String deptIdStr : deptIdArray) {
                    try {
                        Long deptId = Long.valueOf(deptIdStr.trim());
                        if (deptId.equals(currentUser.getDeptId())) {
                            canParticipate = true;
                            break;
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无效的部门ID
                    }
                }
            }
            if (!canParticipate) {
                return error("您没有权限参加该考试");
            }

            // 检查是否已经参加过考试
            ExamRecord existingRecord = new ExamRecord();
            existingRecord.setExamScheduleId(examId);
            existingRecord.setUserId(studentId);
            List<ExamRecord> existingRecords = examRecordService.selectExamRecordList(existingRecord);

            if (!existingRecords.isEmpty()) {
                ExamRecord record = existingRecords.get(0);
                if ("2".equals(record.getStatus())) {
                    return error("您已经完成了这次考试");
                } else if ("1".equals(record.getStatus())) {
                    // 考试进行中，返回考试数据
                    Map<String, Object> examData = buildExamData(examSchedule, record);
                    return success(examData);
                }
            }

            // 检查考试时间
            Date now = new Date();
            if (now.before(examSchedule.getStartTime())) {
                return error("考试尚未开始");
            }
            if (now.after(examSchedule.getEndTime())) {
                return error("考试已结束");
            }

            // 创建新的考试记录
            ExamRecord examRecord = new ExamRecord();
            examRecord.setExamScheduleId(examId);
            examRecord.setUserId(studentId);
            examRecord.setPaperId(examSchedule.getPaperId()); // 设置试卷ID
            examRecord.setStartTime(now);
            examRecord.setStatus("1"); // 1-进行中
            examRecord.setCreateBy(SecurityUtils.getUsername());
            examRecord.setCreateTime(now);

            int result = examRecordService.insertExamRecord(examRecord);

            if (result > 0) {
                Map<String, Object> examData = buildExamData(examSchedule, examRecord);
                return success(examData);
            } else {
                return error("考试初始化失败");
            }
        } catch (Exception e) {
            logger.error("开始考试失败", e);
            return error("开始考试失败：" + e.getMessage());
        }
    }

    /**
     * 构建考试数据
     */
    private Map<String, Object> buildExamData(ExamSchedule examSchedule, ExamRecord examRecord) {
        Map<String, Object> examData = new HashMap<>();
        examData.put("examId", examSchedule.getExamId());
        examData.put("examName", examSchedule.getExamName());
        examData.put("duration", examSchedule.getDuration());
        examData.put("totalScore", 100); // 默认总分100
        examData.put("passingScore", 60); // 默认及格分60
        examData.put("recordId", examRecord.getRecordId());
        examData.put("startTime", examRecord.getStartTime());

        // 获取试卷模板题目
        if (examSchedule.getTemplateId() != null) {
            try {
                PaperTemplate template = paperTemplateService.selectPaperTemplateByTemplateId(examSchedule.getTemplateId());
                if (template != null && template.getQuestionList() != null) {
                    // 为每个题目的questionBank设置分数
                    List<PaperTemplateQuestion> questions = template.getQuestionList();
                    for (PaperTemplateQuestion question : questions) {
                        if (question.getQuestionBank() != null) {
                            // 将PaperTemplateQuestion的score设置到QuestionBank中
                            question.getQuestionBank().setScore(question.getScore());
                        }
                    }
                    examData.put("questions", questions);
                    examData.put("totalScore", template.getTotalScore() != null ? template.getTotalScore() : 100);
                } else {
                    examData.put("questions", new ArrayList<>());
                }
            } catch (Exception e) {
                logger.warn("获取试卷模板失败: {}", e.getMessage());
                examData.put("questions", new ArrayList<>());
            }
        } else {
            examData.put("questions", new ArrayList<>());
        }

        return examData;
    }

    /**
     * 计算考试分数
     */
    private Integer calculateExamScore(Long examId, Map<String, Object> answers) {
        try {
            // 获取考试安排信息
            ExamSchedule examSchedule = examScheduleService.selectExamScheduleByExamId(examId);
            if (examSchedule == null || examSchedule.getTemplateId() == null) {
                logger.warn("考试安排或试卷模板不存在，examId: {}", examId);
                return 0;
            }

            // 获取试卷模板题目
            PaperTemplate template = paperTemplateService.selectPaperTemplateByTemplateId(examSchedule.getTemplateId());
            if (template == null || template.getQuestionList() == null || template.getQuestionList().isEmpty()) {
                logger.warn("试卷模板或题目不存在，templateId: {}", examSchedule.getTemplateId());
                return 0;
            }

            int totalScore = 0;
            List<PaperTemplateQuestion> questions = template.getQuestionList();

            logger.info("开始计算考试分数，题目数量: {}, 学生答案: {}", questions.size(), answers);

            for (PaperTemplateQuestion question : questions) {
                if (question.getQuestionBank() == null) {
                    continue;
                }

                Long questionId = question.getQuestionId();
                String correctAnswer = question.getQuestionBank().getAnswer();
                Object studentAnswerObj = answers.get(questionId.toString());

                if (studentAnswerObj == null) {
                    logger.debug("题目 {} 学生未作答", questionId);
                    continue;
                }

                String studentAnswer = normalizeAnswer(studentAnswerObj);
                String normalizedCorrectAnswer = correctAnswer != null ? correctAnswer.trim() : "";
                String questionType = question.getQuestionBank().getQuestionType();

                logger.info("题目 {} - 类型: {}, 正确答案: {}, 学生答案: {}",
                    questionId, questionType, normalizedCorrectAnswer, studentAnswer);

                // 使用题目类型进行答案比较
                boolean isCorrect = isAnswerCorrect(studentAnswer, normalizedCorrectAnswer, questionType);

                if (isCorrect) {
                    int questionScore = question.getScore() != null ? question.getScore().intValue() : 0;
                    totalScore += questionScore;
                    logger.info("题目 {} 答对，得分: {}", questionId, questionScore);
                } else {
                    logger.info("题目 {} 答错", questionId);
                }
            }

            logger.info("考试评分完成，总分: {}", totalScore);
            return totalScore;
        } catch (Exception e) {
            logger.error("计算考试分数失败", e);
            return 0;
        }
    }

    /**
     * 标准化答案格式
     */
    private String normalizeAnswer(Object answerObj) {
        if (answerObj == null) {
            return "";
        }

        if (answerObj instanceof String) {
            String answer = ((String) answerObj).trim();

            // 处理可能的数组格式字符串 "[A, B, C]"
            if (answer.startsWith("[") && answer.endsWith("]")) {
                answer = answer.substring(1, answer.length() - 1);
                // 分割、排序、重新组合
                String[] options = answer.split("[,\\s]+");
                return java.util.Arrays.stream(options)
                        .filter(opt -> !opt.trim().isEmpty())
                        .map(String::trim)
                        .sorted()
                        .collect(java.util.stream.Collectors.joining(","));
            }

            return answer;
        }

        if (answerObj instanceof List) {
            // 多选题答案是数组，需要排序后连接
            @SuppressWarnings("unchecked")
            List<String> answerList = (List<String>) answerObj;
            return answerList.stream()
                    .filter(s -> s != null && !s.trim().isEmpty())
                    .map(String::trim)
                    .sorted()
                    .collect(java.util.stream.Collectors.joining(","));
        }

        // 处理其他类型，可能是数组的toString格式
        String answer = answerObj.toString().trim();
        if (answer.startsWith("[") && answer.endsWith("]")) {
            answer = answer.substring(1, answer.length() - 1);
            String[] options = answer.split("[,\\s]+");
            return java.util.Arrays.stream(options)
                    .filter(opt -> !opt.trim().isEmpty())
                    .map(String::trim)
                    .sorted()
                    .collect(java.util.stream.Collectors.joining(","));
        }

        return answer;
    }

    /**
     * 构建详细的考试结果
     */
    private Map<String, Object> buildDetailedExamResult(ExamRecord examRecord, ExamSchedule schedule) {
        Map<String, Object> result = new HashMap<>();

        // 基本信息
        result.put("examName", schedule != null ? schedule.getExamName() : "未知考试");
        result.put("score", examRecord.getScore() != null ? examRecord.getScore() : 0);
        result.put("examTime", examRecord.getStartTime());
        result.put("submitTime", examRecord.getSubmitTime());

        // 计算用时（分钟）
        if (examRecord.getStartTime() != null && examRecord.getSubmitTime() != null) {
            long duration = (examRecord.getSubmitTime().getTime() - examRecord.getStartTime().getTime()) / (1000 * 60);
            result.put("duration", duration);
        } else {
            result.put("duration", 0);
        }

        // 获取试卷模板信息和答题详情
        if (schedule != null && schedule.getTemplateId() != null) {
            try {
                PaperTemplate template = paperTemplateService.selectPaperTemplateByTemplateId(schedule.getTemplateId());
                if (template != null) {
                    // 设置正确的总分和及格分
                    result.put("totalScore", template.getTotalScore() != null ? template.getTotalScore() : 100);
                    result.put("passScore", schedule.getPassingScore() != null ? schedule.getPassingScore() : 60);

                    // 构建答题详情
                    List<Map<String, Object>> questionResults = buildQuestionResults(template, examRecord);
                    result.put("questionResults", questionResults);

                    // 计算统计信息
                    Map<String, Object> statistics = calculateStatistics(questionResults, (Integer) result.get("score"), (Integer) result.get("totalScore"));
                    result.putAll(statistics);
                } else {
                    // 模板不存在时的默认值
                    result.put("totalScore", 100);
                    result.put("passScore", 60);
                    result.put("questionResults", new ArrayList<>());
                    setDefaultStatistics(result);
                }
            } catch (Exception e) {
                logger.error("获取试卷模板失败", e);
                result.put("totalScore", 100);
                result.put("passScore", 60);
                result.put("questionResults", new ArrayList<>());
                setDefaultStatistics(result);
            }
        } else {
            // 没有模板信息时的默认值
            result.put("totalScore", 100);
            result.put("passScore", 60);
            result.put("questionResults", new ArrayList<>());
            setDefaultStatistics(result);
        }

        return result;
    }

    /**
     * 构建答题详情
     */
    private List<Map<String, Object>> buildQuestionResults(PaperTemplate template, ExamRecord examRecord) {
        List<Map<String, Object>> questionResults = new ArrayList<>();

        if (template.getQuestionList() == null || template.getQuestionList().isEmpty()) {
            return questionResults;
        }

        // 解析学生答案
        Map<String, Object> studentAnswers = parseStudentAnswers(examRecord.getAnswers());

        int questionNumber = 1;
        for (PaperTemplateQuestion question : template.getQuestionList()) {
            if (question.getQuestionBank() == null) {
                continue;
            }

            Map<String, Object> questionResult = new HashMap<>();
            QuestionBank questionBank = question.getQuestionBank();

            // 基本信息
            questionResult.put("questionNumber", questionNumber++);
            questionResult.put("questionId", question.getQuestionId());
            questionResult.put("questionTitle", questionBank.getContent());
            questionResult.put("questionType", getQuestionTypeText(questionBank.getQuestionType()));
            questionResult.put("totalScore", question.getScore() != null ? question.getScore().intValue() : 0);

            // 答案信息
            String correctAnswer = questionBank.getAnswer();
            Object studentAnswerObj = studentAnswers.get(question.getQuestionId().toString());
            String studentAnswer = normalizeAnswer(studentAnswerObj);

            questionResult.put("correctAnswer", correctAnswer);
            questionResult.put("studentAnswer", studentAnswer);

            // 判断是否正确并计算得分 - 使用题目类型进行准确比较
            boolean isCorrect = isAnswerCorrect(studentAnswer, correctAnswer, questionBank.getQuestionType());
            questionResult.put("isCorrect", isCorrect);
            questionResult.put("score", isCorrect ? question.getScore().intValue() : 0);

            logger.debug("题目 {} 答案比较: 学生答案={}, 正确答案={}, 题目类型={}, 结果={}",
                question.getQuestionId(), studentAnswer, correctAnswer, questionBank.getQuestionType(), isCorrect);

            questionResults.add(questionResult);
        }

        return questionResults;
    }

    /**
     * 解析学生答案
     */
    private Map<String, Object> parseStudentAnswers(String answersJson) {
        Map<String, Object> answers = new HashMap<>();
        if (answersJson == null || answersJson.trim().isEmpty()) {
            return answers;
        }

        try {
            // 使用Jackson解析JSON
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> parsedAnswers = objectMapper.readValue(answersJson, Map.class);
            answers.putAll(parsedAnswers);

            logger.info("成功使用Jackson解析学生答案: {}", answers);
        } catch (Exception e) {
            logger.warn("使用Jackson解析学生答案失败，尝试简单解析: {}", answersJson, e);

            // 备用的简单解析方法 - 修复多选题答案解析问题
            try {
                String cleanJson = answersJson.trim();
                if (cleanJson.startsWith("{") && cleanJson.endsWith("}")) {
                    // 移除大括号
                    cleanJson = cleanJson.substring(1, cleanJson.length() - 1);

                    // 改进的解析方法：手动查找键值对边界
                    // 避免使用复杂正则表达式，采用更可靠的字符串解析

                    int i = 0;
                    while (i < cleanJson.length()) {
                        // 跳过空白字符
                        while (i < cleanJson.length() && Character.isWhitespace(cleanJson.charAt(i))) {
                            i++;
                        }
                        if (i >= cleanJson.length()) break;

                        // 查找键的结束位置（等号或冒号前）
                        int keyStart = i;
                        while (i < cleanJson.length() && cleanJson.charAt(i) != '=' && cleanJson.charAt(i) != ':') {
                            i++;
                        }
                        if (i >= cleanJson.length()) break;

                        String key = cleanJson.substring(keyStart, i).trim().replaceAll("^\"|\"$", "");

                        // 跳过分隔符（= 或 :）
                        i++;

                        // 跳过空白字符
                        while (i < cleanJson.length() && Character.isWhitespace(cleanJson.charAt(i))) {
                            i++;
                        }
                        if (i >= cleanJson.length()) break;

                        // 查找值的结束位置
                        int valueStart = i;
                        int valueEnd = i;

                        // 如果值以引号开始，查找匹配的结束引号
                        if (cleanJson.charAt(i) == '"') {
                            i++; // 跳过开始引号
                            valueStart = i;
                            while (i < cleanJson.length() && cleanJson.charAt(i) != '"') {
                                i++;
                            }
                            valueEnd = i;
                            if (i < cleanJson.length()) i++; // 跳过结束引号
                        } else {
                            // 查找下一个键值对的开始位置（下一个键名=或键名:）
                            while (i < cleanJson.length()) {
                                if (cleanJson.charAt(i) == ',') {
                                    // 检查逗号后是否是新的键值对
                                    int nextPos = i + 1;
                                    while (nextPos < cleanJson.length() && Character.isWhitespace(cleanJson.charAt(nextPos))) {
                                        nextPos++;
                                    }

                                    // 查找下一个等号或冒号的位置
                                    int nextEqualPos = nextPos;
                                    while (nextEqualPos < cleanJson.length() &&
                                           cleanJson.charAt(nextEqualPos) != '=' &&
                                           cleanJson.charAt(nextEqualPos) != ':' &&
                                           cleanJson.charAt(nextEqualPos) != ',') {
                                        nextEqualPos++;
                                    }

                                    // 如果找到了等号或冒号，说明这是新键值对的开始
                                    if (nextEqualPos < cleanJson.length() &&
                                        (cleanJson.charAt(nextEqualPos) == '=' || cleanJson.charAt(nextEqualPos) == ':')) {
                                        valueEnd = i;
                                        i++; // 跳过逗号
                                        break;
                                    }
                                }
                                i++;
                            }
                            if (valueEnd == valueStart) {
                                valueEnd = i;
                            }
                        }

                        String value = cleanJson.substring(valueStart, valueEnd).trim().replaceAll("^\"|\"$", "");

                        if (!key.isEmpty()) {
                            answers.put(key, value);
                            logger.debug("解析键值对: {} = {}", key, value);
                        }

                        // 跳过逗号和空白字符
                        while (i < cleanJson.length() && (cleanJson.charAt(i) == ',' || Character.isWhitespace(cleanJson.charAt(i)))) {
                            i++;
                        }
                    }
                }

                logger.info("使用备用方法解析学生答案: {}", answers);
            } catch (Exception e2) {
                logger.error("解析学生答案完全失败: {}", answersJson, e2);
            }
        }

        return answers;
    }

    /**
     * 获取题目类型文本
     */
    private String getQuestionTypeText(String questionType) {
        if (questionType == null) {
            return "未知";
        }

        switch (questionType) {
            case "0": return "单选题";
            case "1": return "多选题";
            case "2": return "判断题";
            case "3": return "简答题";
            default: return "未知";
        }
    }

    /**
     * 计算统计信息
     */
    private Map<String, Object> calculateStatistics(List<Map<String, Object>> questionResults, Integer score, Integer totalScore) {
        Map<String, Object> statistics = new HashMap<>();

        int correctCount = 0;
        int wrongCount = 0;

        for (Map<String, Object> questionResult : questionResults) {
            Boolean isCorrect = (Boolean) questionResult.get("isCorrect");
            if (Boolean.TRUE.equals(isCorrect)) {
                correctCount++;
            } else {
                wrongCount++;
            }
        }

        statistics.put("correctCount", correctCount);
        statistics.put("wrongCount", wrongCount);
        statistics.put("accuracy", questionResults.isEmpty() ? 0 : Math.round((correctCount * 100.0) / questionResults.size()));
        statistics.put("ranking", 0); // 暂时不实现排名功能

        return statistics;
    }

    /**
     * 设置默认统计信息
     */
    private void setDefaultStatistics(Map<String, Object> result) {
        result.put("correctCount", 0);
        result.put("wrongCount", 0);
        result.put("accuracy", 0);
        result.put("ranking", 0);
    }

    /**
     * 转换考试状态为前端期望的格式
     */
    private String convertStatusForFrontend(ExamSchedule schedule, Date now, Long studentId) {
        // 检查学生是否已经参加过这个考试
        try {
            ExamRecord recordQuery = new ExamRecord();
            recordQuery.setExamScheduleId(schedule.getExamId());
            recordQuery.setUserId(studentId);
            List<ExamRecord> records = examRecordService.selectExamRecordList(recordQuery);

            if (!records.isEmpty()) {
                ExamRecord record = records.get(0);
                if ("2".equals(record.getStatus())) {
                    return "completed"; // 学生已完成考试
                } else if ("1".equals(record.getStatus())) {
                    return "inProgress"; // 学生正在考试
                }
            }
        } catch (Exception e) {
            logger.warn("查询考试记录失败: {}", e.getMessage());
        }

        // 根据时间判断实际状态
        if (schedule.getStartTime() != null && schedule.getEndTime() != null) {
            if (now.before(schedule.getStartTime())) {
                return "upcoming"; // 未开始
            } else if (now.after(schedule.getEndTime())) {
                return "ended"; // 已结束
            } else {
                return "inProgress"; // 进行中（可以开始考试）
            }
        }

        // 如果没有时间信息，根据数据库状态判断
        String dbStatus = schedule.getStatus();
        switch (dbStatus) {
            case "0":
                return "upcoming";
            case "1":
                return "inProgress";
            case "2":
                return "ended";
            default:
                return "upcoming";
        }
    }

    /**
     * 获取考试详情
     */
    @GetMapping("/exams/{examId}/detail")
    public AjaxResult getExamDetail(@PathVariable("examId") Long examId)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 检查考试是否存在
            ExamSchedule examSchedule = examScheduleService.selectExamScheduleByExamId(examId);
            if (examSchedule == null) {
                return error("考试不存在");
            }

            // 验证学生是否有权限查看该考试
            SysUser currentUser = userService.selectUserById(studentId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("学生信息不完整，请联系管理员");
            }

            // 检查考试是否分配给学生所在班级
            boolean canView = false;
            if (examSchedule.getDeptIds() != null && !examSchedule.getDeptIds().isEmpty()) {
                String[] deptIdArray = examSchedule.getDeptIds().split(",");
                for (String deptIdStr : deptIdArray) {
                    try {
                        Long deptId = Long.valueOf(deptIdStr.trim());
                        if (deptId.equals(currentUser.getDeptId())) {
                            canView = true;
                            break;
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无效的部门ID
                    }
                }
            }
            if (!canView) {
                return error("您没有权限查看该考试");
            }

            // 检查学生的考试记录
            ExamRecord recordQuery = new ExamRecord();
            recordQuery.setExamScheduleId(examId);
            recordQuery.setUserId(studentId);
            List<ExamRecord> records = examRecordService.selectExamRecordList(recordQuery);

            if (records.isEmpty()) {
                return error("您尚未开始此考试");
            }

            ExamRecord currentRecord = records.get(0);

            // 构建考试详情数据
            Map<String, Object> examData = new HashMap<>();
            examData.put("examId", examId);
            examData.put("examName", examSchedule.getExamName());
            examData.put("duration", examSchedule.getDuration());
            examData.put("startTime", currentRecord.getStartTime());
            examData.put("status", currentRecord.getStatus());
            examData.put("totalScore", 100); // 默认总分

            // 获取试卷模板题目
            if (examSchedule.getTemplateId() != null) {
                try {
                    PaperTemplate template = paperTemplateService.selectPaperTemplateByTemplateId(examSchedule.getTemplateId());
                    if (template != null && template.getQuestionList() != null) {
                        // 为每个题目的questionBank设置分数
                        List<PaperTemplateQuestion> questions = template.getQuestionList();
                        for (PaperTemplateQuestion question : questions) {
                            if (question.getQuestionBank() != null) {
                                // 将PaperTemplateQuestion的score设置到QuestionBank中
                                question.getQuestionBank().setScore(question.getScore());
                            }
                        }
                        examData.put("questions", questions);
                        examData.put("totalScore", template.getTotalScore() != null ? template.getTotalScore() : 100);
                    } else {
                        examData.put("questions", new ArrayList<>());
                    }
                } catch (Exception e) {
                    logger.warn("获取试卷模板失败: {}", e.getMessage());
                    examData.put("questions", new ArrayList<>());
                }
            } else {
                examData.put("questions", new ArrayList<>());
            }

            return success(examData);
        } catch (Exception e) {
            logger.error("获取考试详情失败", e);
            return error("获取考试详情失败：" + e.getMessage());
        }
    }

    /**
     * 提交考试
     */
    @PostMapping("/exams/{examId}/submit")
    public AjaxResult submitExam(@PathVariable("examId") Long examId,
                                @RequestBody Map<String, Object> answers)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 查找考试记录
            ExamRecord recordQuery = new ExamRecord();
            recordQuery.setExamScheduleId(examId);
            recordQuery.setUserId(studentId);
            List<ExamRecord> records = examRecordService.selectExamRecordList(recordQuery);

            if (records.isEmpty()) {
                return error("考试记录不存在");
            }

            ExamRecord examRecord = records.get(0);
            if (!"1".equals(examRecord.getStatus())) {
                return error("考试已提交，不能重复提交");
            }

            // 更新考试记录
            examRecord.setSubmitTime(new Date());
            examRecord.setStatus("2"); // 已完成

            // 使用JSON格式存储答案，避免toString()的格式问题
            try {
                com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                String answersJson = objectMapper.writeValueAsString(answers);
                examRecord.setAnswers(answersJson);
                logger.info("答案存储格式: {}", answersJson);
            } catch (Exception e) {
                logger.warn("JSON序列化答案失败，使用toString: {}", e.getMessage());
                examRecord.setAnswers(answers.toString()); // 备用方案
            }

            // 实现自动评分逻辑
            Integer score = calculateExamScore(examId, answers);
            examRecord.setScore(score);

            // 自动收集错题
            try {
                collectWrongQuestions(examId, studentId, answers);
            } catch (Exception e) {
                logger.warn("收集错题失败: {}", e.getMessage());
                // 错题收集失败不影响考试提交
            }

            examRecord.setUpdateBy(SecurityUtils.getUsername());
            examRecord.setUpdateTime(new Date());

            int result = examRecordService.updateExamRecord(examRecord);
            if (result > 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("score", score);
                response.put("message", "考试提交成功");
                return success(response);
            } else {
                return error("考试提交失败");
            }
        } catch (Exception e) {
            logger.error("提交考试失败", e);
            return error("提交考试失败：" + e.getMessage());
        }
    }

    /**
     * 收集错题到错题本
     */
    private void collectWrongQuestions(Long examId, Long studentId, Map<String, Object> answers) {
        try {
            // 获取考试安排信息
            ExamSchedule examSchedule = examScheduleService.selectExamScheduleByExamId(examId);
            if (examSchedule == null || examSchedule.getTemplateId() == null) {
                logger.warn("考试安排或试卷模板不存在，无法收集错题，examId: {}", examId);
                return;
            }

            // 获取试卷模板题目
            PaperTemplate template = paperTemplateService.selectPaperTemplateByTemplateId(examSchedule.getTemplateId());
            if (template == null || template.getQuestionList() == null || template.getQuestionList().isEmpty()) {
                logger.warn("试卷模板或题目不存在，无法收集错题，templateId: {}", examSchedule.getTemplateId());
                return;
            }

            // 遍历题目，检查答案是否正确
            for (PaperTemplateQuestion templateQuestion : template.getQuestionList()) {
                QuestionBank question = templateQuestion.getQuestionBank();
                if (question == null) continue;


                Object studentAnswerObj = answers.get(question.getQuestionId().toString());

                if (studentAnswerObj != null) {
                    String studentAnswer = studentAnswerObj.toString();
                    String correctAnswer = question.getAnswer();

                    // 判断答案是否错误
                    if (!isAnswerCorrect(studentAnswer, correctAnswer, question.getQuestionType())) {
                        // 添加到错题本
                        wrongQuestionService.addWrongQuestion(
                            studentId,
                            question.getQuestionId(),
                            examId,
                            null, // 考试错题，homeworkId为null
                            studentAnswer,
                            correctAnswer,
                            question.getContent(),
                            question.getQuestionType(),
                            question.getCourseName(), // 使用课程名作为科目
                            question.getDifficulty()
                        );
                    }
                }
            }

            logger.info("考试错题收集完成，examId: {}, studentId: {}", examId, studentId);
        } catch (Exception e) {
            logger.error("收集考试错题失败", e);
            throw e;
        }
    }

    /**
     * 判断答案是否正确
     */
    private boolean isAnswerCorrect(String studentAnswer, String correctAnswer, String questionType) {
        if (studentAnswer == null || correctAnswer == null) {
            return false;
        }

        // 去除空格并转换为小写进行比较
        String student = studentAnswer.trim().toLowerCase();
        String correct = correctAnswer.trim().toLowerCase();

        // 根据题目类型进行不同的比较逻辑
        switch (questionType) {
            case "0": // 单选题
            case "单选题":
                return student.equals(correct);
            case "2": // 判断题
            case "判断题":
                // 判断题特殊处理：前端使用A/B，题库存储T/F
                return isJudgmentAnswerCorrect(student, correct);
            case "1": // 多选题
            case "多选题":
                // 多选题需要排序后比较
                return normalizeMultipleChoice(student).equals(normalizeMultipleChoice(correct));
            case "3": // 简答题
            case "填空题":
            case "简答题":
                // 简答题可以包含关键词匹配逻辑，这里简化为完全匹配
                return student.equals(correct);
            default:
                return student.equals(correct);
        }
    }

    /**
     * 判断题答案比较
     * 处理前端A/B与题库T/F的映射关系
     */
    private boolean isJudgmentAnswerCorrect(String studentAnswer, String correctAnswer) {
        // 标准化学生答案：A->T, B->F
        String normalizedStudent = normalizeJudgmentAnswer(studentAnswer);
        // 标准化正确答案：确保格式一致
        String normalizedCorrect = normalizeJudgmentAnswer(correctAnswer);

        logger.debug("判断题答案转换: 学生原答案={}, 转换后={}, 正确原答案={}, 转换后={}",
            studentAnswer, normalizedStudent, correctAnswer, normalizedCorrect);

        return normalizedStudent.equals(normalizedCorrect);
    }

    /**
     * 标准化判断题答案
     * A -> T (正确)
     * B -> F (错误)
     * T -> T (保持)
     * F -> F (保持)
     */
    private String normalizeJudgmentAnswer(String answer) {
        if (answer == null) return "";

        String normalized = answer.trim().toLowerCase();
        switch (normalized) {
            case "a":
                return "t"; // A表示正确，转换为T
            case "b":
                return "f"; // B表示错误，转换为F
            case "t":
            case "true":
            case "正确":
                return "t";
            case "f":
            case "false":
            case "错误":
                return "f";
            default:
                // 如果是其他格式，尝试直接返回
                return normalized;
        }
    }

    /**
     * 标准化多选题答案
     */
    private String normalizeMultipleChoice(String answer) {
        if (answer == null || answer.trim().isEmpty()) {
            return "";
        }

        // 处理可能的数组格式 "[A, B, C]" 或 "A,B,C"
        String cleanAnswer = answer.trim();

        // 如果是数组格式，去掉方括号
        if (cleanAnswer.startsWith("[") && cleanAnswer.endsWith("]")) {
            cleanAnswer = cleanAnswer.substring(1, cleanAnswer.length() - 1);
        }

        // 分割选项，去除空格，排序后重新组合
        String[] options = cleanAnswer.split("[,\\s]+");
        java.util.Arrays.sort(options);

        // 过滤空字符串并重新组合
        return java.util.Arrays.stream(options)
                .filter(opt -> !opt.trim().isEmpty())
                .map(String::trim)
                .map(String::toUpperCase)
                .collect(java.util.stream.Collectors.joining(","));
    }

    /**
     * 获取考试结果
     */
    @GetMapping("/exams/{examId}/result")
    public AjaxResult getExamResult(@PathVariable("examId") Long examId)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 查找考试记录
            ExamRecord recordQuery = new ExamRecord();
            recordQuery.setExamScheduleId(examId);
            recordQuery.setUserId(studentId);
            List<ExamRecord> records = examRecordService.selectExamRecordList(recordQuery);

            if (records.isEmpty()) {
                return error("考试记录不存在");
            }

            ExamRecord examRecord = records.get(0);
            if (!"2".equals(examRecord.getStatus())) {
                return error("考试尚未完成");
            }

            // 获取考试安排信息
            ExamSchedule schedule = examScheduleService.selectExamScheduleByExamId(examId);

            // 构建详细的考试结果
            Map<String, Object> result = buildDetailedExamResult(examRecord, schedule);

            // 判断是否及格
            Integer totalScore = (Integer) result.get("totalScore");
            Integer passingScore = (Integer) result.get("passScore");
            result.put("passed", examRecord.getScore() != null && examRecord.getScore() >= passingScore);

            return success(result);
        } catch (Exception e) {
            logger.error("获取考试结果失败", e);
            return error("获取考试结果失败：" + e.getMessage());
        }
    }

    // ==================== 学习工具接口 ====================

    // 笔记管理功能已移至 StudentNotesController
    // 请使用 GET /student/notes 接口

    // 新增笔记功能已移至 StudentNotesController
    // 请使用 POST /student/notes 接口

    // 修改笔记功能已移至 StudentNotesController
    // 请使用 PUT /student/notes 接口

    // 删除笔记功能已移至 StudentNotesController
    // 请使用 DELETE /student/notes/{noteId} 接口

    // 搜索笔记功能已移至 StudentNotesController
    // 请使用 GET /student/notes/search 接口

    // ==================== 收藏功能接口 ====================

    /**
     * 添加收藏
     */
    @PostMapping("/favorites")
    public AjaxResult addFavorite(@RequestBody Map<String, Object> favoriteData)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该实现添加收藏的逻辑
            // 需要检查资源是否存在，是否已收藏等
            return success("收藏成功");
        } catch (Exception e) {
            logger.error("添加收藏失败", e);
            return error("收藏失败：" + e.getMessage());
        }
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/favorites")
    public AjaxResult removeFavorite(@RequestBody Map<String, Object> favoriteData)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该实现取消收藏的逻辑
            return success("取消收藏成功");
        } catch (Exception e) {
            logger.error("取消收藏失败", e);
            return error("取消收藏失败：" + e.getMessage());
        }
    }

    /**
     * 获取我的收藏
     */
    @GetMapping("/favorites")
    public AjaxResult getMyFavorites(@RequestParam(required = false) String resourceType)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该查询学生的收藏列表
            // 暂时返回空列表
            List<Map<String, Object>> favorites = new ArrayList<>();
            return success(favorites);
        } catch (Exception e) {
            logger.error("获取收藏列表失败", e);
            return error("获取收藏列表失败");
        }
    }

    // ==================== 学习提醒和任务接口 ====================

    /**
     * 获取学习提醒
     */
    @GetMapping("/reminders")
    public AjaxResult getStudyReminders()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该查询学生的学习提醒
            // 暂时返回模拟数据
            List<Map<String, Object>> reminders = new ArrayList<>();

            Map<String, Object> reminder1 = new HashMap<>();
            reminder1.put("id", 1L);
            reminder1.put("title", "高等数学作业截止提醒");
            reminder1.put("content", "高等数学第三章作业将于明天23:59截止，请及时完成");
            reminder1.put("type", "homework");
            reminder1.put("priority", "high");
            reminder1.put("dueTime", new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
            reminders.add(reminder1);

            return success(reminders);
        } catch (Exception e) {
            logger.error("获取学习提醒失败", e);
            return error("获取学习提醒失败");
        }
    }

    /**
     * 获取今日学习任务
     */
    @GetMapping("/today-tasks")
    public AjaxResult getTodayTasks()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该查询今日的学习任务
            // 暂时返回模拟数据
            List<Map<String, Object>> tasks = new ArrayList<>();

            Map<String, Object> task1 = new HashMap<>();
            task1.put("id", 1L);
            task1.put("title", "完成高等数学作业");
            task1.put("type", "homework");
            task1.put("status", "pending");
            task1.put("priority", "high");
            task1.put("estimatedTime", 120); // 预计用时（分钟）
            tasks.add(task1);

            Map<String, Object> task2 = new HashMap<>();
            task2.put("id", 2L);
            task2.put("title", "复习英语单词");
            task2.put("type", "study");
            task2.put("status", "completed");
            task2.put("priority", "medium");
            task2.put("estimatedTime", 30);
            tasks.add(task2);

            return success(tasks);
        } catch (Exception e) {
            logger.error("获取今日学习任务失败", e);
            return error("获取今日学习任务失败");
        }
    }

    /**
     * 获取学习建议
     */
    @GetMapping("/study-suggestions")
    public AjaxResult getStudySuggestions()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该基于学生的学习情况生成个性化建议
            // 暂时返回模拟数据
            List<Map<String, Object>> suggestions = new ArrayList<>();

            Map<String, Object> suggestion1 = new HashMap<>();
            suggestion1.put("id", 1L);
            suggestion1.put("title", "加强数学基础练习");
            suggestion1.put("content", "根据您的作业完成情况，建议多做基础练习题");
            suggestion1.put("type", "study_method");
            suggestion1.put("priority", "high");
            suggestions.add(suggestion1);

            return success(suggestions);
        } catch (Exception e) {
            logger.error("获取学习建议失败", e);
            return error("获取学习建议失败");
        }
    }

    // ==================== 学习报告接口 ====================

    /**
     * 获取个人学习报告
     */
    @GetMapping("/reports/personal")
    public AjaxResult getPersonalStudyReport()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            Map<String, Object> report = new HashMap<>();

            // 基本统计
            Map<String, Object> basicStats = new HashMap<>();
            basicStats.put("totalStudyDays", 45);
            basicStats.put("totalStudyHours", 120);
            basicStats.put("averageStudyTime", 2.67); // 小时/天
            basicStats.put("completedCourses", 3);
            basicStats.put("inProgressCourses", 5);
            report.put("basicStats", basicStats);

            // 学习进度
            List<Map<String, Object>> courseProgress = new ArrayList<>();
            Map<String, Object> progress1 = new HashMap<>();
            progress1.put("courseName", "高等数学");
            progress1.put("progress", 85);
            progress1.put("studyHours", 40);
            courseProgress.add(progress1);
            report.put("courseProgress", courseProgress);

            // 成绩分析
            Map<String, Object> gradeAnalysis = new HashMap<>();
            gradeAnalysis.put("averageScore", 82.5);
            gradeAnalysis.put("highestScore", 95);
            gradeAnalysis.put("lowestScore", 68);
            gradeAnalysis.put("passRate", 100);
            report.put("gradeAnalysis", gradeAnalysis);

            return success(report);
        } catch (Exception e) {
            logger.error("获取个人学习报告失败", e);
            return error("获取学习报告失败");
        }
    }

    /**
     * 获取学习概览
     */
    @GetMapping("/reports/overview")
    public AjaxResult getStudyOverview()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            Map<String, Object> overview = new HashMap<>();
            overview.put("totalCourses", 8);
            overview.put("completedHomework", 15);
            overview.put("pendingHomework", 3);
            overview.put("totalExams", 5);
            overview.put("averageScore", 82.5);
            overview.put("studyStreak", 7); // 连续学习天数
            overview.put("weeklyStudyTime", 18); // 本周学习时间

            return success(overview);
        } catch (Exception e) {
            logger.error("获取学习概览失败", e);
            return error("获取学习概览失败");
        }
    }

    /**
     * 获取学习趋势分析
     */
    @GetMapping("/reports/trend")
    public AjaxResult getStudyTrend(@RequestParam(defaultValue = "30") int days)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // TODO: 这里应该查询指定天数内的学习数据
            // 暂时返回空数据，等待实现学习记录跟踪功能
            List<Map<String, Object>> trendData = new ArrayList<>();

            // 返回空的趋势数据，避免误导性的随机数据
            for (int i = days - 1; i >= 0; i--) {
                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", new Date(System.currentTimeMillis() - i * 24 * 60 * 60 * 1000L));
                dayData.put("studyTime", 0); // 默认学习时间为0
                dayData.put("completedTasks", 0); // 默认完成任务数为0
                dayData.put("score", 0); // 默认分数为0
                trendData.add(dayData);
            }

            return success(trendData);
        } catch (Exception e) {
            logger.error("获取学习趋势分析失败", e);
            return error("获取学习趋势失败");
        }
    }

    /**
     * 获取课程学习分析
     */
    @GetMapping("/reports/course-analysis")
    public AjaxResult getCourseStudyAnalysis()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // TODO: 这里应该分析学生各课程的学习情况
            // 暂时返回空数据，等待实现学习分析功能
            List<Map<String, Object>> courseAnalysis = new ArrayList<>();

            // 返回空的分析数据，避免误导性的静态数据
            // 实际实现时应该从学习记录、作业成绩、考试成绩等数据中分析

            return success(courseAnalysis);
        } catch (Exception e) {
            logger.error("获取课程学习分析失败", e);
            return error("获取课程分析失败");
        }
    }

    /**
     * 获取学习效率分析
     */
    @GetMapping("/reports/efficiency")
    public AjaxResult getStudyEfficiencyAnalysis()
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // TODO: 这里应该分析学生的学习效率数据
            // 暂时返回空数据，等待实现学习行为跟踪功能
            Map<String, Object> efficiency = new HashMap<>();
            efficiency.put("averageStudyTime", 0.0); // 平均每日学习时间
            efficiency.put("peakStudyHour", 0); // 最佳学习时段
            efficiency.put("studyEfficiencyScore", 0); // 学习效率评分
            efficiency.put("focusTime", 0); // 平均专注时间（分钟）
            efficiency.put("breakFrequency", 0); // 平均休息频率

            // 学习习惯分析
            List<String> habits = new ArrayList<>();
            // 暂时返回空的习惯分析，等待实现数据分析功能
            efficiency.put("studyHabits", habits);

            return success(efficiency);
        } catch (Exception e) {
            logger.error("获取学习效率分析失败", e);
            return error("获取效率分析失败");
        }
    }

    /**
     * 导出学习报告
     */
    @PostMapping("/reports/export")
    public AjaxResult exportStudyReport(@RequestParam String reportType,
                                        @RequestParam(defaultValue = "pdf") String format)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该实现报告导出功能
            // 暂时返回成功消息
            Map<String, Object> result = new HashMap<>();
            result.put("message", "报告导出成功");
            result.put("downloadUrl", "/download/report_" + studentId + "_" + System.currentTimeMillis() + "." + format);

            return success(result);
        } catch (Exception e) {
            logger.error("导出学习报告失败", e);
            return error("导出报告失败");
        }
    }

    /**
     * 生成学习证书
     */
    @PostMapping("/reports/certificate/{courseId}")
    public AjaxResult generateStudyCertificate(@PathVariable Long courseId)
    {
        try {
            Long studentId = SecurityUtils.getUserId();

            // 这里应该检查课程完成情况，生成学习证书
            // 暂时返回成功消息
            Course course = courseService.selectCourseByCourseId(courseId);
            if (course == null) {
                return error("课程不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("message", "证书生成成功");
            result.put("certificateUrl", "/download/certificate_" + studentId + "_" + courseId + ".pdf");
            result.put("courseName", course.getCourseName());
            result.put("generateTime", new Date());

            return success(result);
        } catch (Exception e) {
            logger.error("生成学习证书失败", e);
            return error("生成证书失败");
        }
    }
}
