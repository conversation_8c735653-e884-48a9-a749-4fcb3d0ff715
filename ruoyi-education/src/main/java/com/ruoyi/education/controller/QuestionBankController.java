package com.ruoyi.education.controller;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.education.domain.QuestionBank;
import com.ruoyi.education.domain.vo.QuestionBankVO;
import com.ruoyi.education.service.IQuestionBankService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.education.domain.vo.QuestionBankImportResultVO;
import com.ruoyi.education.service.IQuestionBankImportService;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 题库管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/education/bank")
public class QuestionBankController extends BaseController
{
    @Autowired
    private IQuestionBankService questionBankService;

    @Autowired
    private IQuestionBankImportService questionBankImportService;

    /**
     * 查询题库管理列表
     */
    @PreAuthorize("@ss.hasPermi('education:bank:list')")
    @GetMapping("/list")
    public TableDataInfo list(QuestionBank questionBank)
    {
        startPage();
        List<QuestionBank> list = questionBankService.selectQuestionBankList(questionBank);

        // 转换为VO并清洗数据
        List<QuestionBankVO> voList = new ArrayList<>();
        for (QuestionBank bank : list) {
            QuestionBankVO vo = new QuestionBankVO();
            BeanUtils.copyProperties(bank, vo);

            // 数据清洗：处理options字段
            Object rawOptions = bank.getOptions();
            try {
                while (rawOptions instanceof String) {
                    rawOptions = JSON.parse((String) rawOptions);
                }
            } catch (Exception e) {
                rawOptions = new ArrayList<>();
            }

            if (!(rawOptions instanceof List)) {
                rawOptions = new ArrayList<>();
            }

            vo.setOptions((List<Map<String, Object>>) rawOptions);
            voList.add(vo);
        }

        return getDataTable(voList);
    }

    /**
     * 导出题库管理列表
     */
    @PreAuthorize("@ss.hasPermi('education:bank:export')")
    @Log(title = "题库管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuestionBank questionBank)
    {
        List<QuestionBank> list = questionBankService.selectQuestionBankList(questionBank);
        ExcelUtil<QuestionBank> util = new ExcelUtil<QuestionBank>(QuestionBank.class);
        util.exportExcel(response, list, "题库管理数据");
    }

    /**
     * 获取题库管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('education:bank:query')")
    @SuppressWarnings("unchecked")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        QuestionBank questionBank = questionBankService.selectQuestionBankByQuestionId(questionId);
        QuestionBankVO vo = new QuestionBankVO();
        BeanUtils.copyProperties(questionBank, vo);

        // --- 后端数据清洗最终方案 ---
        Object rawOptions = questionBank.getOptions();
        // 只要还是字符串，就持续解析，直到它变成一个对象/数组或解析失败
        try {
            while (rawOptions instanceof String) {
                rawOptions = JSON.parse((String) rawOptions);
            }
        } catch (Exception e) {
            // 如果最终解析失败，则置为空数组以防页面崩溃
            rawOptions = new ArrayList<>();
        }

        // 确保如果数据不是List，也转换为空列表
        if (!(rawOptions instanceof List)) {
            rawOptions = new ArrayList<>();
        }

        vo.setOptions((List<Map<String, Object>>) rawOptions);

        return success(vo);
    }

    /**
     * 新增题库管理
     */
    @PreAuthorize("@ss.hasPermi('education:bank:add')")
    @Log(title = "题库管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody List<QuestionBank> questionBanks)
    {
        if (questionBanks == null || questionBanks.isEmpty()) {
            return AjaxResult.error("提交的题目列表不能为空");
        }
        return toAjax(questionBankService.insertQuestionBanks(questionBanks));
    }

    /**
     * 修改题库管理
     */
    @PreAuthorize("@ss.hasPermi('education:bank:edit')")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionBank questionBank)
    {
        return toAjax(questionBankService.updateQuestionBank(questionBank));
    }

    /**
     * 删除题库管理
     */
    @PreAuthorize("@ss.hasPermi('education:bank:remove')")
    @Log(title = "题库管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(questionBankService.deleteQuestionBankByQuestionIds(questionIds));
    }

    /**
     * 下载题库导入模板
     */
    @PreAuthorize("@ss.hasPermi('education:bank:import')")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        byte[] templateData = questionBankImportService.downloadTemplate();

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=question_bank_import_template.xlsx");

        response.getOutputStream().write(templateData);
        response.getOutputStream().flush();
    }

    /**
     * 导入题库数据
     */
    @PreAuthorize("@ss.hasPermi('education:bank:import')")
    @Log(title = "题库管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, Long courseId, boolean updateSupport) throws Exception
    {
        if (file == null || file.isEmpty()) {
            return error("请选择要导入的Excel文件");
        }

        if (courseId == null) {
            return error("请选择所属课程");
        }

        QuestionBankImportResultVO result = questionBankImportService.importQuestionBank(file, courseId, updateSupport);

        if (result.getSuccess()) {
            return AjaxResult.success(result.getSummary(), result);
        } else {
            return AjaxResult.success(result.getSummary(), result);
        }
    }
}
