package com.ruoyi.education.domain.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 题库导入结果VO
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class QuestionBankImportResultVO {

    /** 导入是否成功 */
    private Boolean success;

    /** 总行数 */
    private Integer totalRows;

    /** 成功导入行数 */
    private Integer successRows;

    /** 失败行数 */
    private Integer failRows;

    /** 跳过行数（重复数据等） */
    private Integer skipRows;

    /** 错误信息列表 */
    private List<ImportError> errors;

    /** 警告信息列表 */
    private List<ImportWarning> warnings;

    /** 导入耗时（毫秒） */
    private Long duration;

    public QuestionBankImportResultVO() {
        this.success = true;
        this.totalRows = 0;
        this.successRows = 0;
        this.failRows = 0;
        this.skipRows = 0;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
    }

    /**
     * 添加错误信息
     */
    public void addError(int rowNumber, String message) {
        this.errors.add(new ImportError(rowNumber, message));
        this.failRows++;
        this.success = false;
    }

    /**
     * 添加警告信息
     */
    public void addWarning(int rowNumber, String message) {
        this.warnings.add(new ImportWarning(rowNumber, message));
    }

    /**
     * 增加成功行数
     */
    public void incrementSuccess() {
        this.successRows++;
    }

    /**
     * 增加跳过行数
     */
    public void incrementSkip() {
        this.skipRows++;
    }

    /**
     * 设置总行数
     */
    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows;
    }

    /**
     * 获取导入摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("导入完成！");
        summary.append("总计：").append(totalRows).append("行，");
        summary.append("成功：").append(successRows).append("行，");
        summary.append("失败：").append(failRows).append("行，");
        summary.append("跳过：").append(skipRows).append("行");
        
        if (duration != null) {
            summary.append("，耗时：").append(duration).append("ms");
        }
        
        return summary.toString();
    }

    /**
     * 错误信息内部类
     */
    @Data
    public static class ImportError {
        /** 行号 */
        private Integer rowNumber;
        /** 错误信息 */
        private String message;

        public ImportError(Integer rowNumber, String message) {
            this.rowNumber = rowNumber;
            this.message = message;
        }
    }

    /**
     * 警告信息内部类
     */
    @Data
    public static class ImportWarning {
        /** 行号 */
        private Integer rowNumber;
        /** 警告信息 */
        private String message;

        public ImportWarning(Integer rowNumber, String message) {
            this.rowNumber = rowNumber;
            this.message = message;
        }
    }
}
