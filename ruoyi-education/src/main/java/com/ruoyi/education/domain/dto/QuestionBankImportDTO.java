package com.ruoyi.education.domain.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 题库导入DTO
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class QuestionBankImportDTO {

    /** 题目类型（0单选 1多选 2判断 3简答） */
    @Excel(name = "题目类型", cellType = Excel.ColumnType.NUMERIC, type = Excel.Type.IMPORT)
    @NotNull(message = "题目类型不能为空")
    private Integer questionType;

    /** 题目内容 */
    @Excel(name = "题目内容", type = Excel.Type.IMPORT)
    @NotBlank(message = "题目内容不能为空")
    private String content;

    /** 选项A */
    @Excel(name = "选项A", type = Excel.Type.IMPORT)
    private String optionA;

    /** 选项B */
    @Excel(name = "选项B", type = Excel.Type.IMPORT)
    private String optionB;

    /** 选项C */
    @Excel(name = "选项C", type = Excel.Type.IMPORT)
    private String optionC;

    /** 选项D */
    @Excel(name = "选项D", type = Excel.Type.IMPORT)
    private String optionD;

    /** 正确答案 */
    @Excel(name = "正确答案", type = Excel.Type.IMPORT)
    @NotBlank(message = "正确答案不能为空")
    private String answer;

    /** 解析 */
    @Excel(name = "解析", type = Excel.Type.IMPORT)
    private String analysis;

    /** 难度（1简单 2中等 3困难） */
    @Excel(name = "难度", cellType = Excel.ColumnType.NUMERIC, type = Excel.Type.IMPORT)
    private Integer difficulty;

    /** 知识点 */
    @Excel(name = "知识点", type = Excel.Type.IMPORT)
    private String knowledgePoint;

    /** 行号（用于错误提示） */
    private Integer rowNumber;

    /**
     * 获取选项JSON字符串
     */
    public String getOptionsJson() {
        if (questionType == null || questionType == 2 || questionType == 3) {
            return null; // 判断题和简答题不需要选项
        }

        StringBuilder options = new StringBuilder();
        options.append("{");
        boolean hasOption = false;

        if (optionA != null && !optionA.trim().isEmpty()) {
            options.append("\"A\":\"").append(optionA.trim().replace("\"", "\\\"")).append("\"");
            hasOption = true;
        }
        if (optionB != null && !optionB.trim().isEmpty()) {
            if (hasOption) options.append(",");
            options.append("\"B\":\"").append(optionB.trim().replace("\"", "\\\"")).append("\"");
            hasOption = true;
        }
        if (optionC != null && !optionC.trim().isEmpty()) {
            if (hasOption) options.append(",");
            options.append("\"C\":\"").append(optionC.trim().replace("\"", "\\\"")).append("\"");
            hasOption = true;
        }
        if (optionD != null && !optionD.trim().isEmpty()) {
            if (hasOption) options.append(",");
            options.append("\"D\":\"").append(optionD.trim().replace("\"", "\\\"")).append("\"");
            hasOption = true;
        }

        options.append("}");
        return hasOption ? options.toString() : null;
    }

    /**
     * 校验数据有效性
     */
    public String validate() {
        // 校验题目类型
        if (questionType == null || questionType < 0 || questionType > 3) {
            return "题目类型必须是0(单选)、1(多选)、2(判断)、3(简答)中的一个";
        }

        // 校验题目内容
        if (content == null || content.trim().isEmpty()) {
            return "题目内容不能为空";
        }
        if (content.length() > 500) {
            return "题目内容长度不能超过500字符";
        }

        // 校验答案
        if (answer == null || answer.trim().isEmpty()) {
            return "正确答案不能为空";
        }

        // 根据题目类型校验
        switch (questionType) {
            case 0: // 单选题
                return validateSingleChoice();
            case 1: // 多选题
                return validateMultipleChoice();
            case 2: // 判断题
                return validateJudgment();
            case 3: // 简答题
                return validateEssay();
            default:
                return "未知的题目类型";
        }
    }

    private String validateSingleChoice() {
        // 单选题必须有至少2个选项
        int optionCount = 0;
        if (optionA != null && !optionA.trim().isEmpty()) optionCount++;
        if (optionB != null && !optionB.trim().isEmpty()) optionCount++;
        if (optionC != null && !optionC.trim().isEmpty()) optionCount++;
        if (optionD != null && !optionD.trim().isEmpty()) optionCount++;

        if (optionCount < 2) {
            return "单选题至少需要2个选项";
        }

        // 答案必须是A、B、C、D中的一个
        String ans = answer.trim().toUpperCase();
        if (!ans.matches("^[A-D]$")) {
            return "单选题答案必须是A、B、C、D中的一个";
        }

        // 检查答案对应的选项是否存在
        switch (ans) {
            case "A":
                if (optionA == null || optionA.trim().isEmpty()) {
                    return "答案是A，但选项A为空";
                }
                break;
            case "B":
                if (optionB == null || optionB.trim().isEmpty()) {
                    return "答案是B，但选项B为空";
                }
                break;
            case "C":
                if (optionC == null || optionC.trim().isEmpty()) {
                    return "答案是C，但选项C为空";
                }
                break;
            case "D":
                if (optionD == null || optionD.trim().isEmpty()) {
                    return "答案是D，但选项D为空";
                }
                break;
        }

        return null; // 校验通过
    }

    private String validateMultipleChoice() {
        // 多选题必须有至少2个选项
        int optionCount = 0;
        if (optionA != null && !optionA.trim().isEmpty()) optionCount++;
        if (optionB != null && !optionB.trim().isEmpty()) optionCount++;
        if (optionC != null && !optionC.trim().isEmpty()) optionCount++;
        if (optionD != null && !optionD.trim().isEmpty()) optionCount++;

        if (optionCount < 2) {
            return "多选题至少需要2个选项";
        }

        // 答案必须是A、B、C、D的组合
        String ans = answer.trim().toUpperCase();
        if (!ans.matches("^[A-D]+$")) {
            return "多选题答案必须是A、B、C、D的组合，如AB、ABC等";
        }

        // 检查答案对应的选项是否都存在
        for (char c : ans.toCharArray()) {
            switch (c) {
                case 'A':
                    if (optionA == null || optionA.trim().isEmpty()) {
                        return "答案包含A，但选项A为空";
                    }
                    break;
                case 'B':
                    if (optionB == null || optionB.trim().isEmpty()) {
                        return "答案包含B，但选项B为空";
                    }
                    break;
                case 'C':
                    if (optionC == null || optionC.trim().isEmpty()) {
                        return "答案包含C，但选项C为空";
                    }
                    break;
                case 'D':
                    if (optionD == null || optionD.trim().isEmpty()) {
                        return "答案包含D，但选项D为空";
                    }
                    break;
            }
        }

        return null; // 校验通过
    }

    private String validateJudgment() {
        // 判断题答案必须是预定义的值
        String ans = answer.trim();
        if (!ans.matches("^(是|否|对|错|正确|错误|true|false|TRUE|FALSE)$")) {
            return "判断题答案必须是：是、否、对、错、正确、错误、true、false中的一个";
        }

        return null; // 校验通过
    }

    private String validateEssay() {
        // 简答题答案长度校验
        if (answer.length() > 1000) {
            return "简答题答案长度不能超过1000字符";
        }

        return null; // 校验通过
    }
}
