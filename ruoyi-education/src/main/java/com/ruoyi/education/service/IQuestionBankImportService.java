package com.ruoyi.education.service;

import com.ruoyi.education.domain.vo.QuestionBankImportResultVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 题库导入服务接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IQuestionBankImportService {

    /**
     * 导入题库Excel文件
     *
     * @param file Excel文件
     * @param courseId 课程ID
     * @param updateSupport 是否支持更新已存在的题目
     * @return 导入结果
     */
    QuestionBankImportResultVO importQuestionBank(MultipartFile file, Long courseId, boolean updateSupport);

    /**
     * 下载题库导入模板
     *
     * @return 模板文件字节数组
     */
    byte[] downloadTemplate();

    /**
     * 验证Excel文件格式
     *
     * @param file Excel文件
     * @return 验证结果消息，null表示验证通过
     */
    String validateFile(MultipartFile file);
}
