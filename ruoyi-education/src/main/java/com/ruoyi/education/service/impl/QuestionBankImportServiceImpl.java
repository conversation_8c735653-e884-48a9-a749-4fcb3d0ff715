package com.ruoyi.education.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.education.domain.QuestionBank;
import com.ruoyi.education.domain.dto.QuestionBankImportDTO;
import com.ruoyi.education.domain.vo.QuestionBankImportResultVO;
import com.ruoyi.education.service.IQuestionBankImportService;
import com.ruoyi.education.service.IQuestionBankService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 题库导入服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class QuestionBankImportServiceImpl implements IQuestionBankImportService {

    private static final Logger logger = LoggerFactory.getLogger(QuestionBankImportServiceImpl.class);

    @Autowired
    private IQuestionBankService questionBankService;

    /** Excel模板表头 */
    private static final String[] TEMPLATE_HEADERS = {
        "题目类型", "题目内容", "选项A", "选项B", "选项C", "选项D",
        "正确答案", "解析", "难度", "知识点"
    };

    /** 支持的文件类型 */
    private static final String[] SUPPORTED_EXTENSIONS = {".xlsx", ".xls"};

    /** 最大文件大小（5MB） */
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    @Override
    @Transactional
    public QuestionBankImportResultVO importQuestionBank(MultipartFile file, Long courseId, boolean updateSupport) {
        long startTime = System.currentTimeMillis();
        QuestionBankImportResultVO result = new QuestionBankImportResultVO();

        try {
            // 1. 文件格式校验
            String validateMsg = validateFile(file);
            if (validateMsg != null) {
                result.addError(0, validateMsg);
                return result;
            }

            // 2. 解析Excel文件
            List<QuestionBankImportDTO> importList = parseExcelFile(file, result);
            if (importList.isEmpty()) {
                result.addError(0, "Excel文件中没有有效的数据行");
                return result;
            }

            result.setTotalRows(importList.size());

            // 3. 数据校验和转换
            List<QuestionBank> validQuestions = new ArrayList<>();
            for (QuestionBankImportDTO dto : importList) {
                String validationError = dto.validate();
                if (validationError != null) {
                    result.addError(dto.getRowNumber(), validationError);
                    continue;
                }

                // 检查重复题目（使用增强版检查）
                if (isDuplicateQuestion(dto, courseId)) {
                    if (updateSupport) {
                        result.addWarning(dto.getRowNumber(), "题目已存在，将进行更新");
                    } else {
                        result.addWarning(dto.getRowNumber(), "题目已存在，已跳过");
                        result.incrementSkip();
                        continue;
                    }
                }

                // 转换为QuestionBank对象
                QuestionBank question = convertToQuestionBank(dto, courseId);
                validQuestions.add(question);
            }

            // 4. 批量保存到数据库
            if (!validQuestions.isEmpty()) {
                saveQuestions(validQuestions, updateSupport, result);
            }

            // 5. 设置导入结果
            result.setDuration(System.currentTimeMillis() - startTime);

            logger.info("题库导入完成，课程ID：{}，{}", courseId, result.getSummary());

        } catch (Exception e) {
            logger.error("题库导入失败", e);
            result.addError(0, "导入过程中发生异常：" + e.getMessage());
        }

        return result;
    }

    @Override
    public byte[] downloadTemplate() {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("题库导入模板");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < TEMPLATE_HEADERS.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(TEMPLATE_HEADERS[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 4000); // 设置列宽
            }

            // 添加示例数据
            addTemplateExamples(sheet, workbook);

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            logger.error("生成题库导入模板失败", e);
            throw new RuntimeException("生成模板失败");
        }
    }

    @Override
    public String validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "请选择要导入的Excel文件";
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return "文件大小不能超过5MB";
        }

        // 检查文件扩展名
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return "文件名不能为空";
        }

        boolean supportedType = false;
        for (String ext : SUPPORTED_EXTENSIONS) {
            if (fileName.toLowerCase().endsWith(ext)) {
                supportedType = true;
                break;
            }
        }

        if (!supportedType) {
            return "只支持.xlsx和.xls格式的Excel文件";
        }

        return null; // 验证通过
    }

    /**
     * 解析Excel文件
     */
    private List<QuestionBankImportDTO> parseExcelFile(MultipartFile file, QuestionBankImportResultVO result) {
        List<QuestionBankImportDTO> importList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<QuestionBankImportDTO> util = new ExcelUtil<>(QuestionBankImportDTO.class);
            List<QuestionBankImportDTO> list = util.importExcel(inputStream);

            // 设置行号
            for (int i = 0; i < list.size(); i++) {
                QuestionBankImportDTO dto = list.get(i);
                dto.setRowNumber(i + 2); // Excel行号从2开始（第1行是表头）

                // 基本数据校验
                if (StringUtils.isNotEmpty(dto.getContent())) {
                    importList.add(dto);
                } else {
                    result.addWarning(dto.getRowNumber(), "题目内容为空，已跳过该行");
                }
            }

        } catch (Exception e) {
            logger.error("解析Excel文件失败", e);
            result.addError(0, "Excel文件解析失败：" + e.getMessage());
        }

        return importList;
    }

    /**
     * 检查是否为重复题目（增强版）
     */
    private boolean isDuplicateQuestion(QuestionBankImportDTO dto, Long courseId) {
        // 标准化题目内容
        String normalizedContent = normalizeContent(dto.getContent());

        QuestionBank query = new QuestionBank();
        query.setCourseId(courseId);
        query.setQuestionType(dto.getQuestionType().toString());

        List<QuestionBank> candidates = questionBankService.selectQuestionBankList(query);

        // 精确匹配检查
        for (QuestionBank candidate : candidates) {
            String candidateContent = normalizeContent(candidate.getContent());

            // 内容完全匹配
            if (normalizedContent.equals(candidateContent)) {
                // 对于选择题，还需要比较选项
                if (isChoiceQuestion(dto.getQuestionType())) {
                    if (isSameOptions(candidate.getOptions(), dto.getOptionsJson())) {
                        return true;
                    }
                } else {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查是否为重复题目（兼容旧方法）
     */
    private boolean isDuplicateQuestion(String content, Long courseId) {
        QuestionBankImportDTO dto = new QuestionBankImportDTO();
        dto.setContent(content);
        dto.setQuestionType(0); // 默认单选题
        return isDuplicateQuestion(dto, courseId);
    }

    /**
     * 标准化题目内容
     */
    private String normalizeContent(String content) {
        if (content == null) return "";

        return content.trim()
                     .toLowerCase()
                     .replaceAll("\\s+", " ")        // 多个空格替换为单个空格
                     .replaceAll("[\\r\\n]+", " ")   // 换行符替换为空格
                     .replaceAll("^\\s+|\\s+$", ""); // 去除首尾空格
    }

    /**
     * 判断是否为选择题
     */
    private boolean isChoiceQuestion(Integer questionType) {
        return questionType != null && (questionType == 0 || questionType == 1); // 单选或多选
    }

    /**
     * 比较选项是否相同
     */
    private boolean isSameOptions(Object existingOptions, String newOptionsJson) {
        if (existingOptions == null && newOptionsJson == null) {
            return true;
        }

        if (existingOptions == null || newOptionsJson == null) {
            return false;
        }

        try {
            // 将现有选项转换为标准格式
            String existingJson = normalizeOptionsJson(existingOptions.toString());
            String newJson = normalizeOptionsJson(newOptionsJson);

            return existingJson.equals(newJson);
        } catch (Exception e) {
            logger.warn("选项比较失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标准化选项JSON格式
     */
    private String normalizeOptionsJson(String optionsJson) {
        if (optionsJson == null || optionsJson.trim().isEmpty()) {
            return "{}";
        }

        try {
            // 解析JSON并重新格式化，确保键值对顺序一致
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(optionsJson);

            // 创建有序的选项映射
            java.util.Map<String, String> sortedOptions = new java.util.TreeMap<>();
            for (String key : jsonObject.keySet()) {
                String value = jsonObject.getString(key);
                if (value != null && !value.trim().isEmpty()) {
                    sortedOptions.put(key.toUpperCase(), value.trim().toLowerCase());
                }
            }

            return com.alibaba.fastjson.JSON.toJSONString(sortedOptions);
        } catch (Exception e) {
            logger.warn("选项JSON标准化失败: {}", e.getMessage());
            return optionsJson;
        }
    }

    /**
     * 转换DTO为实体对象
     */
    private QuestionBank convertToQuestionBank(QuestionBankImportDTO dto, Long courseId) {
        QuestionBank question = new QuestionBank();
        question.setCourseId(courseId);
        question.setContent(dto.getContent());
        question.setQuestionType(dto.getQuestionType().toString());
        question.setAnswer(dto.getAnswer());
        question.setOptions(dto.getOptionsJson());
        question.setExplanation(dto.getAnalysis());
        question.setDifficulty(dto.getDifficulty() != null ? dto.getDifficulty().toString() : "2");
        // 知识点字段暂时存储在remark中
        question.setRemark(dto.getKnowledgePoint());
        question.setCreateBy(SecurityUtils.getUsername());
        question.setCreateTime(DateUtils.getNowDate());
        return question;
    }

    /**
     * 批量保存题目
     */
    private void saveQuestions(List<QuestionBank> questions, boolean updateSupport, QuestionBankImportResultVO result) {
        for (QuestionBank question : questions) {
            try {
                // 根据原始DTO检查是否需要更新
                QuestionBankImportDTO originalDto = findOriginalDto(questions, question);
                if (updateSupport && originalDto != null && isDuplicateQuestion(originalDto, question.getCourseId())) {
                    // 更新现有题目
                    QuestionBank existing = findExistingQuestion(originalDto, question.getCourseId());
                    if (existing != null) {
                        question.setQuestionId(existing.getQuestionId());
                        question.setUpdateBy(SecurityUtils.getUsername());
                        question.setUpdateTime(DateUtils.getNowDate());
                        questionBankService.updateQuestionBank(question);
                    }
                } else {
                    // 新增题目
                    questionBankService.insertQuestionBank(question);
                }
                result.incrementSuccess();
            } catch (Exception e) {
                logger.error("保存题目失败：{}", question.getContent(), e);
                result.addError(0, "保存题目失败：" + question.getContent());
            }
        }
    }

    /**
     * 查找现有题目（增强版）
     */
    private QuestionBank findExistingQuestion(QuestionBankImportDTO dto, Long courseId) {
        String normalizedContent = normalizeContent(dto.getContent());

        QuestionBank query = new QuestionBank();
        query.setCourseId(courseId);
        query.setQuestionType(dto.getQuestionType().toString());

        List<QuestionBank> candidates = questionBankService.selectQuestionBankList(query);

        // 精确匹配查找
        for (QuestionBank candidate : candidates) {
            String candidateContent = normalizeContent(candidate.getContent());

            if (normalizedContent.equals(candidateContent)) {
                // 对于选择题，还需要比较选项
                if (isChoiceQuestion(dto.getQuestionType())) {
                    if (isSameOptions(candidate.getOptions(), dto.getOptionsJson())) {
                        return candidate;
                    }
                } else {
                    return candidate;
                }
            }
        }

        return null;
    }

    /**
     * 查找现有题目（兼容旧方法）
     */
    private QuestionBank findExistingQuestion(String content, Long courseId) {
        QuestionBankImportDTO dto = new QuestionBankImportDTO();
        dto.setContent(content);
        dto.setQuestionType(0); // 默认单选题
        return findExistingQuestion(dto, courseId);
    }

    /**
     * 从题目列表中找到对应的原始DTO
     */
    private QuestionBankImportDTO findOriginalDto(List<QuestionBank> questions, QuestionBank target) {
        // 这里需要维护一个DTO到QuestionBank的映射关系
        // 为了简化，我们创建一个临时DTO
        QuestionBankImportDTO dto = new QuestionBankImportDTO();
        dto.setContent(target.getContent());

        // 从questionType字符串转换为Integer
        try {
            dto.setQuestionType(Integer.parseInt(target.getQuestionType()));
        } catch (NumberFormatException e) {
            dto.setQuestionType(0); // 默认单选题
        }

        // 从options对象转换为JSON字符串
        if (target.getOptions() != null) {
            try {
                String optionsJson = target.getOptions().toString();
                // 这里需要根据实际的options格式进行转换
                // 暂时直接使用toString()结果
            } catch (Exception e) {
                logger.warn("选项转换失败: {}", e.getMessage());
            }
        }

        return dto;
    }

    /**
     * 添加模板示例数据
     */
    private void addTemplateExamples(Sheet sheet, Workbook workbook) {
        // 示例数据
        Object[][] examples = {
            {0, "1+1等于多少？", "1", "2", "3", "4", "B", "1+1的基本运算结果是2", 1, "数学基础"},
            {1, "以下哪些是编程语言？", "Java", "Python", "HTML", "CSS", "AB", "Java和Python是编程语言", 2, "计算机基础"},
            {2, "地球是圆的", "", "", "", "", "是", "地球是一个近似球体", 1, "地理常识"},
            {3, "请简述Java的特点", "", "", "", "", "Java具有跨平台、面向对象、安全性高等特点", "考查对Java语言特性的理解", 3, "Java编程"}
        };

        for (int i = 0; i < examples.length; i++) {
            Row row = sheet.createRow(i + 1);
            Object[] example = examples[i];
            for (int j = 0; j < example.length; j++) {
                Cell cell = row.createCell(j);
                if (example[j] != null) {
                    if (example[j] instanceof Number) {
                        cell.setCellValue(((Number) example[j]).doubleValue());
                    } else {
                        cell.setCellValue(example[j].toString());
                    }
                }
            }
        }
    }
}
