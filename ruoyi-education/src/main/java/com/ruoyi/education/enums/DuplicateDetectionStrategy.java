package com.ruoyi.education.enums;

/**
 * 重复题目检测策略枚举
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public enum DuplicateDetectionStrategy {

    /**
     * 仅基于内容检测
     * 只比较题目内容，不考虑题目类型和选项
     */
    CONTENT_ONLY("content_only", "仅基于内容"),

    /**
     * 内容 + 题目类型
     * 比较题目内容和题目类型
     */
    CONTENT_AND_TYPE("content_and_type", "内容+类型"),

    /**
     * 内容 + 类型 + 选项（默认策略）
     * 比较题目内容、题目类型和选项内容（选择题）
     */
    CONTENT_TYPE_OPTIONS("content_type_options", "内容+类型+选项"),

    /**
     * 基于相似度检测
     * 使用字符串相似度算法，允许一定程度的差异
     */
    SIMILARITY_BASED("similarity_based", "相似度检测");

    private final String code;
    private final String description;

    DuplicateDetectionStrategy(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取策略
     */
    public static DuplicateDetectionStrategy fromCode(String code) {
        for (DuplicateDetectionStrategy strategy : values()) {
            if (strategy.getCode().equals(code)) {
                return strategy;
            }
        }
        return CONTENT_TYPE_OPTIONS; // 默认策略
    }

    /**
     * 获取默认策略
     */
    public static DuplicateDetectionStrategy getDefault() {
        return CONTENT_TYPE_OPTIONS;
    }
}
