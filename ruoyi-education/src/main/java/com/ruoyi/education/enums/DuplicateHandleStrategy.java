package com.ruoyi.education.enums;

/**
 * 重复题目处理策略枚举
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public enum DuplicateHandleStrategy {

    /**
     * 跳过已存在的题目
     */
    SKIP(0, "跳过已存在", "遇到重复题目时跳过，不进行任何操作"),

    /**
     * 覆盖已存在的题目
     */
    OVERRIDE(1, "覆盖已存在", "遇到重复题目时用新数据覆盖原有数据"),

    /**
     * 智能合并（预留功能）
     */
    MERGE(2, "智能合并", "保留原有题目，将新题目作为变体添加");

    private final int code;
    private final String name;
    private final String description;

    DuplicateHandleStrategy(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取策略
     */
    public static DuplicateHandleStrategy fromCode(int code) {
        for (DuplicateHandleStrategy strategy : values()) {
            if (strategy.getCode() == code) {
                return strategy;
            }
        }
        return SKIP; // 默认跳过
    }

    /**
     * 获取默认策略
     */
    public static DuplicateHandleStrategy getDefault() {
        return SKIP;
    }

    /**
     * 是否需要更新
     */
    public boolean needUpdate() {
        return this == OVERRIDE || this == MERGE;
    }
}
