<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.education.mapper.ExamRecordMapper">

    <resultMap type="ExamRecord" id="ExamRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="userId"    column="user_id"    />
        <result property="examId"    column="exam_id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="examScheduleId"    column="exam_schedule_id"    />
        <result property="score"    column="score"    />
        <result property="startTime"    column="start_time"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="status"    column="status"    />
        <result property="answers"    column="answers"    />
        <result property="examName"    column="exam_name"    />
        <result property="paperName"    column="paper_name"    />
        <result property="totalScore"    column="total_score"    />
        <result property="passingScore"    column="passing_score"    />
    </resultMap>

    <insert id="insertExamRecord" parameterType="ExamRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into edu_exam_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="examId != null">exam_id,</if>
            <if test="paperId != null">paper_id,</if>
            <if test="examScheduleId != null">exam_schedule_id,</if>
            <if test="score != null">score,</if>
            <if test="startTime != null">start_time,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="answers != null and answers != ''">answers,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="examId != null">#{examId},</if>
            <if test="paperId != null">#{paperId},</if>
            <if test="examScheduleId != null">#{examScheduleId},</if>
            <if test="score != null">#{score},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="answers != null and answers != ''">#{answers},</if>
        </trim>
    </insert>

    <update id="updateExamRecord" parameterType="com.ruoyi.education.domain.ExamRecord">
        update edu_exam_record
        <set>
            <if test="examScheduleId != null">exam_schedule_id = #{examScheduleId},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="score != null">score = #{score},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="answers != null and answers != ''">answers = #{answers},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where record_id = #{recordId}
    </update>

    <select id="selectExamRecordList" parameterType="com.ruoyi.education.domain.ExamRecord" resultMap="ExamRecordResult">
        select r.record_id, r.user_id, r.exam_id, r.paper_id, r.exam_schedule_id, r.score, r.start_time, r.submit_time, r.status, r.answers,
               s.exam_name,
               p.paper_name,
               p.total_score,
               s.passing_score
        from edu_exam_record r
        left join edu_exam_schedule s on r.exam_schedule_id = s.schedule_id
        left join edu_exam_paper p on r.paper_id = p.paper_id
        <where>
            <if test="userId != null ">
                and r.user_id = #{userId}
            </if>
            <if test="examId != null ">
                and r.exam_id = #{examId}
            </if>
            <if test="examScheduleId != null ">
                and r.exam_schedule_id = #{examScheduleId}
            </if>
            <if test="status != null  and status != ''">
                and r.status = #{status}
            </if>
        </where>
    </select>

    <select id="selectMyGrades" parameterType="Long" resultMap="ExamRecordResult">
        SELECT
            er.record_id,
            er.score,
            er.submit_time,
            es.exam_name,
            ep.paper_name,
            ep.total_score,
            es.passing_score
        FROM
            edu_exam_record er
        LEFT JOIN edu_exam_schedule es ON er.exam_schedule_id = es.schedule_id
        LEFT JOIN edu_exam_paper ep ON er.paper_id = ep.paper_id
        WHERE
            er.user_id = #{userId}
        ORDER BY er.submit_time DESC
    </select>


    <select id="selectExamRecordByStudentAndExam" resultMap="ExamRecordResult">
        select * from edu_exam_record
        where user_id = #{studentId} and exam_schedule_id = #{examId}
    </select>

</mapper>
