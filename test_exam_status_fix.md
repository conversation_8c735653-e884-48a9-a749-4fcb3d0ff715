# 考试状态问题修复验证

## 问题描述
教师发布了一场考试，学生去参加并完成了考试。然后这个教师再去发布另外一场考试，这个学生去查看考试的时候发现这个考试已经完结了。

## 数据库表结构确认

### edu_exam_schedule表
- 主键：`schedule_id` (对应实体类的examId字段)
- 其他字段：exam_name, paper_id, template_id, dept_ids等

### edu_exam_record表
- 主键：`record_id`
- 关联字段：`exam_schedule_id` (关联到edu_exam_schedule.schedule_id)
- 其他字段：user_id, exam_id, paper_id, score等

## 问题根因分析

### 1. 数据库字段映射不一致
- `ExamRecord`实体类中有`examId`和`examScheduleId`两个字段
- 在`StudentController.convertStatusForFrontend`方法中使用`examScheduleId`查询
- 但在`ExamRecordMapper.xml`中JOIN关系使用了错误的字段

### 2. JOIN关系错误
- 错误的JOIN条件：`r.exam_id = s.schedule_id`
- 正确的JOIN条件：`r.exam_schedule_id = s.schedule_id`

### 3. 考试记录创建时缺少paperId
- 创建考试记录时没有设置`paperId`字段

## 修复内容

### 1. 修复ExamRecordMapper.xml
- 添加`examScheduleId`字段映射
- 修复JOIN关系
- 添加`examScheduleId`查询条件

### 2. 修复StudentController
- 在创建考试记录时设置`paperId`

### 3. 修复的文件
- `ruoyi-education/src/main/resources/mapper/education/ExamRecordMapper.xml`
- `ruoyi-education/src/main/java/com/ruoyi/education/controller/StudentController.java`

## 验证步骤

### 1. 教师端操作
1. 教师登录系统
2. 创建第一场考试（考试A）
3. 发布考试A

### 2. 学生端操作
1. 学生登录系统
2. 查看考试列表，应该看到考试A状态为"进行中"或"未开始"
3. 参加考试A并完成提交

### 3. 教师端再次操作
1. 教师创建第二场考试（考试B）
2. 发布考试B

### 4. 学生端验证
1. 学生再次查看考试列表
2. 应该看到：
   - 考试A状态为"已完成"
   - 考试B状态为"进行中"或"未开始"（而不是错误的"已结束"）

## 技术细节

### 修复前的问题
```java
// StudentController.convertStatusForFrontend方法
ExamRecord recordQuery = new ExamRecord();
recordQuery.setExamScheduleId(schedule.getExamId()); // 设置examScheduleId
recordQuery.setUserId(studentId);
List<ExamRecord> records = examRecordService.selectExamRecordList(recordQuery);
```

```xml
<!-- ExamRecordMapper.xml中JOIN关系错误 -->
left join edu_exam_schedule s on r.exam_id = s.schedule_id  <!-- 错误 -->
```

### 修复后
```xml
<!-- 正确的JOIN关系和查询条件 -->
<result property="examScheduleId" column="exam_schedule_id" />

<!-- 正确的JOIN条件 -->
left join edu_exam_schedule s on r.exam_schedule_id = s.schedule_id

<!-- 添加examScheduleId查询条件 -->
<where>
    <if test="userId != null ">
        and r.user_id = #{userId}
    </if>
    <if test="examId != null ">
        and r.exam_id = #{examId}
    </if>
    <if test="examScheduleId != null ">
        and r.exam_schedule_id = #{examScheduleId}
    </if>
</where>
```

### 修复的具体文件和位置

#### 1. ExamRecordMapper.xml
- **第71行**：`left join edu_exam_schedule s on r.exam_schedule_id = s.schedule_id`
- **第81行**：添加examScheduleId查询条件
- **第100行**：`LEFT JOIN edu_exam_schedule es ON er.exam_schedule_id = es.schedule_id`

#### 2. StudentController.java
- **第1311行**：添加`examRecord.setPaperId(examSchedule.getPaperId());`

## 预期结果
修复后，学生查看考试列表时：
1. 已参加过的考试显示为"已完成"
2. 新发布的考试显示正确的状态（未开始/进行中）
3. 不会出现新考试被错误标记为"已结束"的问题
