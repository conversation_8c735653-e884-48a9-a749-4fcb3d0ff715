-- 修复edu_exam_record表结构
-- 添加exam_schedule_id字段（如果不存在）

-- 1. 检查字段是否存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'edu_exam_record' 
  AND COLUMN_NAME = 'exam_schedule_id';

-- 2. 如果字段不存在，添加字段
ALTER TABLE edu_exam_record 
ADD COLUMN exam_schedule_id bigint(20) DEFAULT NULL COMMENT '考试安排ID' 
AFTER paper_id;

-- 3. 添加索引
ALTER TABLE edu_exam_record 
ADD INDEX idx_exam_schedule_id (exam_schedule_id);

-- 4. 如果有现有数据，需要更新exam_schedule_id字段
-- 假设原来使用exam_id字段关联到edu_exam_schedule的schedule_id
UPDATE edu_exam_record r 
SET r.exam_schedule_id = r.exam_id 
WHERE r.exam_schedule_id IS NULL AND r.exam_id IS NOT NULL;

-- 5. 验证数据
SELECT 
    r.record_id,
    r.user_id,
    r.exam_id,
    r.exam_schedule_id,
    s.exam_name
FROM edu_exam_record r
LEFT JOIN edu_exam_schedule s ON r.exam_schedule_id = s.schedule_id
LIMIT 10;
