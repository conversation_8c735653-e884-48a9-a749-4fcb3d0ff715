# 题目重复判定优化方案

## 当前判定逻辑

### 判定字段
- **主要字段**：`content`（题目内容）
- **限定范围**：`courseId`（课程ID）

### 判定规则
```java
// 当前逻辑：同一课程下，题目内容完全相同即认为重复
private boolean isDuplicateQuestion(String content, Long courseId) {
    QuestionBank query = new QuestionBank();
    query.setContent(content);           // 题目内容
    query.setCourseId(courseId);         // 课程ID
    List<QuestionBank> existingQuestions = questionBankService.selectQuestionBankList(query);
    return !existingQuestions.isEmpty();
}
```

## 优化方案

### 方案1：基于内容哈希的判定
```java
// 使用题目内容的MD5哈希值进行判定，避免空格、换行等格式差异
private boolean isDuplicateQuestion(String content, Long courseId) {
    String contentHash = DigestUtils.md5Hex(content.trim().toLowerCase());
    // 在数据库中查询相同哈希值的题目
}
```

### 方案2：多字段组合判定
```java
// 基于题目内容 + 题目类型 + 选项内容的组合判定
private boolean isDuplicateQuestion(QuestionBankImportDTO dto, Long courseId) {
    QuestionBank query = new QuestionBank();
    query.setContent(dto.getContent().trim());
    query.setCourseId(courseId);
    query.setQuestionType(dto.getQuestionType().toString());
    
    List<QuestionBank> existingQuestions = questionBankService.selectQuestionBankList(query);
    
    // 进一步比较选项内容
    for (QuestionBank existing : existingQuestions) {
        if (isSameOptions(existing.getOptions(), dto.getOptionsJson())) {
            return true;
        }
    }
    return false;
}
```

### 方案3：相似度判定
```java
// 使用字符串相似度算法，允许一定程度的差异
private boolean isDuplicateQuestion(String content, Long courseId) {
    List<QuestionBank> allQuestions = getAllQuestionsByContent(courseId);
    
    for (QuestionBank question : allQuestions) {
        double similarity = calculateSimilarity(content, question.getContent());
        if (similarity > 0.95) { // 95%以上相似度认为重复
            return true;
        }
    }
    return false;
}
```

## 推荐的改进实现

### 1. 增强的重复判定方法
```java
/**
 * 检查是否为重复题目（增强版）
 */
private boolean isDuplicateQuestion(QuestionBankImportDTO dto, Long courseId) {
    // 1. 基础内容判定
    String normalizedContent = normalizeContent(dto.getContent());
    
    QuestionBank query = new QuestionBank();
    query.setCourseId(courseId);
    query.setQuestionType(dto.getQuestionType().toString());
    
    List<QuestionBank> candidates = questionBankService.selectQuestionBankList(query);
    
    // 2. 精确匹配
    for (QuestionBank candidate : candidates) {
        String candidateContent = normalizeContent(candidate.getContent());
        
        // 内容完全匹配
        if (normalizedContent.equals(candidateContent)) {
            // 对于选择题，还需要比较选项
            if (isChoiceQuestion(dto.getQuestionType())) {
                if (isSameOptions(candidate.getOptions(), dto.getOptionsJson())) {
                    return true;
                }
            } else {
                return true;
            }
        }
    }
    
    return false;
}

/**
 * 标准化题目内容
 */
private String normalizeContent(String content) {
    if (content == null) return "";
    
    return content.trim()
                 .toLowerCase()
                 .replaceAll("\\s+", " ")  // 多个空格替换为单个空格
                 .replaceAll("[\\r\\n]+", " "); // 换行符替换为空格
}

/**
 * 比较选项是否相同
 */
private boolean isSameOptions(Object existingOptions, String newOptionsJson) {
    // 实现选项内容的比较逻辑
    // 考虑选项顺序可能不同的情况
}
```

### 2. 用户可配置的判定策略
```java
public enum DuplicateDetectionStrategy {
    CONTENT_ONLY,           // 仅基于内容
    CONTENT_AND_TYPE,       // 内容 + 题目类型
    CONTENT_TYPE_OPTIONS,   // 内容 + 类型 + 选项
    SIMILARITY_BASED        // 基于相似度
}
```

## 数据库优化

### 1. 添加内容哈希字段
```sql
-- 在题目表中添加内容哈希字段，提高查询效率
ALTER TABLE edu_question_bank 
ADD COLUMN content_hash VARCHAR(32) COMMENT '题目内容哈希值';

-- 创建哈希值索引
CREATE INDEX idx_content_hash ON edu_question_bank(content_hash, course_id);
```

### 2. 创建重复检测视图
```sql
-- 创建重复题目检测视图
CREATE VIEW v_duplicate_questions AS
SELECT 
    course_id,
    content_hash,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(question_id) as question_ids
FROM edu_question_bank 
GROUP BY course_id, content_hash 
HAVING COUNT(*) > 1;
```

## 前端界面优化

### 1. 重复处理策略说明
```javascript
// 在前端界面中提供更详细的说明
const duplicateStrategies = [
  {
    value: 0,
    label: '跳过已存在',
    description: '当发现题目内容相同时，跳过导入，保留原有题目'
  },
  {
    value: 1,
    label: '覆盖已存在',
    description: '当发现题目内容相同时，用新数据覆盖原有题目'
  },
  {
    value: 2,
    label: '智能合并',
    description: '保留原有题目，将新题目作为变体添加'
  }
];
```

### 2. 重复题目预览
```javascript
// 在导入前显示可能重复的题目列表
showDuplicatePreview() {
  // 调用后端API检测重复题目
  // 显示重复题目列表供用户确认
}
```

## 实施建议

### 阶段1：基础优化
1. 实现内容标准化处理
2. 添加题目类型到重复判定
3. 优化数据库查询性能

### 阶段2：高级功能
1. 实现选项内容比较
2. 添加相似度判定
3. 提供用户可配置的判定策略

### 阶段3：用户体验
1. 重复题目预览功能
2. 批量处理重复题目
3. 重复题目管理界面

这样的优化可以让重复判定更加准确和灵活，提升用户的导入体验。
