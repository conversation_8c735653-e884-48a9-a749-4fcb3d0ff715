-- 检查edu_exam_record表结构
DESCRIBE edu_exam_record;

-- 或者使用SHOW COLUMNS
SHOW COLUMNS FROM edu_exam_record;

-- 检查edu_exam_schedule表结构
DESCRIBE edu_exam_schedule;

-- 如果edu_exam_record表中没有exam_schedule_id字段，需要添加
-- ALTER TABLE edu_exam_record ADD COLUMN exam_schedule_id bigint(20) DEFAULT NULL COMMENT '考试安排ID';

-- 检查现有数据
SELECT COUNT(*) FROM edu_exam_record;
SELECT COUNT(*) FROM edu_exam_schedule;

-- 查看edu_exam_record表的示例数据
SELECT * FROM edu_exam_record LIMIT 5;

-- 查看edu_exam_schedule表的示例数据  
SELECT * FROM edu_exam_schedule LIMIT 5;
