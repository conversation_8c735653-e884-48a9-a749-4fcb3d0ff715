# 题库Excel导入模板设计

## Excel模板格式

### 表头设计（第1行）
| 列号 | 字段名 | 字段说明 | 必填 | 数据类型 | 示例 |
|------|--------|----------|------|----------|------|
| A | 题目类型 | 0=单选,1=多选,2=判断,3=简答 | 是 | 数字 | 0 |
| B | 题目内容 | 题目描述 | 是 | 文本 | 以下哪个是正确的？ |
| C | 选项A | 选择题选项A | 否 | 文本 | 选项内容A |
| D | 选项B | 选择题选项B | 否 | 文本 | 选项内容B |
| E | 选项C | 选择题选项C | 否 | 文本 | 选项内容C |
| F | 选项D | 选择题选项D | 否 | 文本 | 选项内容D |
| G | 正确答案 | 答案内容 | 是 | 文本 | A 或 AB 或 是 |
| H | 解析 | 题目解析 | 否 | 文本 | 解题思路说明 |
| I | 难度 | 1=简单,2=中等,3=困难 | 否 | 数字 | 2 |
| J | 知识点 | 知识点标签 | 否 | 文本 | 数学基础 |

### 数据校验规则

#### 1. 题目类型校验
- **单选题(0)**：必须有选项A、B，答案只能是A、B、C、D中的一个
- **多选题(1)**：必须有选项A、B，答案可以是A、B、C、D的组合（如AB、ABC）
- **判断题(2)**：不需要选项，答案只能是"是"、"否"、"对"、"错"、"正确"、"错误"
- **简答题(3)**：不需要选项，答案为文本内容

#### 2. 必填字段校验
- 题目类型：不能为空，必须是0、1、2、3
- 题目内容：不能为空，长度不超过500字符
- 正确答案：不能为空

#### 3. 选项校验
- 单选题/多选题：至少需要2个选项
- 选项内容不能为空（如果填写了选项列）
- 选项长度不超过200字符

#### 4. 答案格式校验
- 单选题：答案必须是A、B、C、D中的一个
- 多选题：答案必须是A、B、C、D的组合，如AB、ABC、ABCD
- 判断题：答案必须是预定义的值
- 简答题：答案长度不超过1000字符

## Excel示例数据

### 单选题示例
```
题目类型: 0
题目内容: 1+1等于多少？
选项A: 1
选项B: 2
选项C: 3
选项D: 4
正确答案: B
解析: 1+1的基本运算结果是2
难度: 1
知识点: 数学基础
```

### 多选题示例
```
题目类型: 1
题目内容: 以下哪些是编程语言？
选项A: Java
选项B: Python
选项C: HTML
选项D: CSS
正确答案: AB
解析: Java和Python是编程语言，HTML和CSS是标记语言
难度: 2
知识点: 计算机基础
```

### 判断题示例
```
题目类型: 2
题目内容: 地球是圆的
选项A: (空)
选项B: (空)
选项C: (空)
选项D: (空)
正确答案: 是
解析: 地球是一个近似球体
难度: 1
知识点: 地理常识
```

### 简答题示例
```
题目类型: 3
题目内容: 请简述Java的特点
选项A: (空)
选项B: (空)
选项C: (空)
选项D: (空)
正确答案: Java具有跨平台、面向对象、安全性高等特点
解析: 考查对Java语言特性的理解
难度: 3
知识点: Java编程
```

## 错误处理机制

### 1. 文件格式校验
- 只支持.xlsx和.xls格式
- 文件大小限制（如5MB）
- 表头格式必须完全匹配

### 2. 数据行校验
- 逐行校验，记录错误行号和错误信息
- 支持部分成功导入（跳过错误行）
- 提供详细的错误报告

### 3. 重复数据处理
- 检查题目内容是否重复
- 提供覆盖或跳过选项

## 导入流程设计

1. **文件上传** → 2. **格式校验** → 3. **数据解析** → 4. **业务校验** → 5. **数据入库** → 6. **结果反馈**
